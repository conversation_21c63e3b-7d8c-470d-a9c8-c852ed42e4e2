#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple API test with Flask
"""

from flask import Flask, jsonify, request
import qrcode
from io import BytesIO
import base64
from datetime import datetime

app = Flask(__name__)

@app.route('/test-qr', methods=['POST'])
def test_qr():
    """Test QR generation"""
    try:
        print("🔍 Testing QR generation...")
        
        # Create QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=4)
        qr.add_data("http://localhost:5000/verify/TEST-001")
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        b64 = base64.b64encode(buffer.getvalue()).decode()
        
        print(f"✅ QR created! Base64 length: {len(b64)}")
        
        return jsonify({
            'success': True,
            'base64': b64,
            'message': 'QR Code created successfully'
        })
        
    except Exception as e:
        print(f"❌ QR error: {e}")
        return jsonify({
            'success': False,
            'message': f'QR generation failed: {str(e)}'
        }), 500

@app.route('/test-bilingual', methods=['POST'])
def test_bilingual():
    """Test bilingual generation"""
    try:
        print("🌐 Testing bilingual generation...")
        
        arabic_content = """
        <div class="arabic-section">
            <h2>عقد اختبار</h2>
            <p>الطرف الأول: أحمد محمد</p>
            <p>الطرف الثاني: شركة قطر</p>
            <p>قيمة العقد: 10,000 ريال قطري</p>
            <p>تاريخ العقد: {}</p>
        </div>
        """.format(datetime.now().strftime('%Y-%m-%d'))
        
        english_content = """
        <div class="english-section">
            <h2>Test Contract</h2>
            <p>First Party: Ahmed Mohammed</p>
            <p>Second Party: Qatar Company</p>
            <p>Contract Value: 10,000 QAR</p>
            <p>Contract Date: {}</p>
        </div>
        """.format(datetime.now().strftime('%Y-%m-%d'))
        
        combined_content = f"""
        <div class="bilingual-contract">
            <div class="arabic-version" style="direction: rtl; text-align: right;">
                <h1>النسخة العربية</h1>
                {arabic_content}
            </div>
            <hr style="margin: 30px 0;">
            <div class="english-version" style="direction: ltr; text-align: left;">
                <h1>English Version</h1>
                {english_content}
            </div>
        </div>
        """
        
        print(f"✅ Bilingual content created! Length: {len(combined_content)}")
        
        return jsonify({
            'success': True,
            'content': combined_content,
            'arabic_content': arabic_content,
            'english_content': english_content,
            'message': 'Bilingual contract created successfully'
        })
        
    except Exception as e:
        print(f"❌ Bilingual error: {e}")
        return jsonify({
            'success': False,
            'message': f'Bilingual generation failed: {str(e)}'
        }), 500

@app.route('/test')
def test_page():
    """Test page"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>API Test</title>
        <script>
            function testQR() {
                fetch('/test-qr', {method: 'POST'})
                .then(r => r.json())
                .then(data => {
                    document.getElementById('qr-result').innerHTML = 
                        data.success ? 
                        `<img src="data:image/png;base64,${data.base64}" style="max-width:200px;">` :
                        `Error: ${data.message}`;
                });
            }
            
            function testBilingual() {
                fetch('/test-bilingual', {method: 'POST'})
                .then(r => r.json())
                .then(data => {
                    document.getElementById('bilingual-result').innerHTML = 
                        data.success ? data.content : `Error: ${data.message}`;
                });
            }
        </script>
    </head>
    <body>
        <h1>Simple API Test</h1>
        
        <h2>QR Code Test</h2>
        <button onclick="testQR()">Test QR</button>
        <div id="qr-result"></div>
        
        <h2>Bilingual Test</h2>
        <button onclick="testBilingual()">Test Bilingual</button>
        <div id="bilingual-result"></div>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("🚀 Starting simple API test server...")
    print("Open: http://localhost:5001/test")
    app.run(debug=True, port=5001)
