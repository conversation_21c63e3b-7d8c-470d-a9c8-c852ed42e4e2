{% extends "base.html" %}

{% block title %}قائمة العقود - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-contract me-2"></i>
                    قائمة العقود
                </h1>
                <div>
                    <a href="{{ url_for('contracts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء عقد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contracts Table -->
    <div class="card shadow">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">العقود</h6>
        </div>
        <div class="card-body">
            {% if contracts.items %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم العقد</th>
                                <th>العنوان</th>
                                <th>الطرف الأول</th>
                                <th>الطرف الثاني</th>
                                <th>النوع</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in contracts.items %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('contracts.view', id=contract.id) }}" class="text-decoration-none">
                                        {{ contract.contract_number }}
                                    </a>
                                </td>
                                <td>{{ contract.title }}</td>
                                <td>{{ contract.first_party_name }}</td>
                                <td>{{ contract.second_party_name }}</td>
                                <td>
                                    {% if contract.contract_type %}
                                        <span class="badge bg-info">{{ contract.contract_type.name }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contract.contract_value %}
                                        {{ contract.contract_value }} {{ contract.currency }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if contract.status == 'active' else 'warning' if contract.status == 'draft' else 'info' if contract.status == 'completed' else 'secondary' }}">
                                        {{ contract.get_status_display() }}
                                    </span>
                                </td>
                                <td>{{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('contracts.view', id=contract.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if contract.can_edit(current_user) %}
                                        <a href="{{ url_for('contracts.edit', id=contract.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="تصدير">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='pdf') }}">
                                                        <i class="fas fa-file-pdf me-1"></i> PDF
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='word') }}">
                                                        <i class="fas fa-file-word me-1"></i> Word
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                        {% if current_user.is_manager() %}
                                        <form method="POST" action="{{ url_for('contracts.delete', id=contract.id) }}" 
                                              style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا العقد؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if contracts.pages > 1 %}
                <nav aria-label="صفحات العقود">
                    <ul class="pagination justify-content-center">
                        {% if contracts.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('contracts.index', page=contracts.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in contracts.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != contracts.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('contracts.index', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if contracts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('contracts.index', page=contracts.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-5">
                    <i class="fas fa-file-contract fa-5x text-gray-300 mb-4"></i>
                    <h4 class="text-gray-600">لا توجد عقود</h4>
                    <p class="text-gray-500 mb-4">لم يتم إنشاء أي عقود بعد</p>
                    <a href="{{ url_for('contracts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء عقد جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-600 {
    color: #6e707e !important;
}

.pagination .page-link {
    color: #667eea;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

.pagination .page-link:hover {
    color: #5a6fd8;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
</style>
{% endblock %}
