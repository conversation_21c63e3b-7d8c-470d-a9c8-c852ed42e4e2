from datetime import datetime
from . import db

class Notification(db.Model):
    __tablename__ = 'notifications'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # info, warning, success, error
    is_read = db.Column(db.Boole<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Optional reference to contract
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)
    
    # Relationships
    user = db.relationship('User', backref='notifications')
    contract = db.relationship('Contract', backref='notifications')
    
    def __init__(self, user_id, title, message, type='info', contract_id=None):
        self.user_id = user_id
        self.title = title
        self.message = message
        self.type = type
        self.contract_id = contract_id
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        db.session.commit()
    
    def to_dict(self):
        """Convert notification to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'contract_id': self.contract_id,
            'contract_number': self.contract.contract_number if self.contract else None
        }
    
    @staticmethod
    def create_contract_notification(user_id, contract, notification_type, custom_message=None):
        """Create contract-related notification"""
        
        notification_templates = {
            'contract_created': {
                'title': 'تم إنشاء عقد جديد',
                'message': f'تم إنشاء العقد رقم {contract.contract_number} بعنوان "{contract.title}"',
                'type': 'success'
            },
            'contract_updated': {
                'title': 'تم تحديث العقد',
                'message': f'تم تحديث العقد رقم {contract.contract_number}',
                'type': 'info'
            },
            'contract_signed': {
                'title': 'تم توقيع العقد',
                'message': f'تم توقيع العقد رقم {contract.contract_number} بالكامل',
                'type': 'success'
            },
            'contract_expiring': {
                'title': 'عقد على وشك الانتهاء',
                'message': f'العقد رقم {contract.contract_number} سينتهي قريباً',
                'type': 'warning'
            },
            'contract_expired': {
                'title': 'انتهى العقد',
                'message': f'انتهت صلاحية العقد رقم {contract.contract_number}',
                'type': 'error'
            }
        }
        
        template = notification_templates.get(notification_type, {
            'title': 'إشعار عقد',
            'message': custom_message or f'تحديث على العقد رقم {contract.contract_number}',
            'type': 'info'
        })
        
        notification = Notification(
            user_id=user_id,
            title=template['title'],
            message=custom_message or template['message'],
            type=template['type'],
            contract_id=contract.id
        )
        
        db.session.add(notification)
        db.session.commit()
        return notification
    
    @staticmethod
    def get_unread_count(user_id):
        """Get count of unread notifications for user"""
        return Notification.query.filter_by(user_id=user_id, is_read=False).count()
    
    @staticmethod
    def get_recent_notifications(user_id, limit=10):
        """Get recent notifications for user"""
        return Notification.query.filter_by(user_id=user_id)\
                                .order_by(Notification.created_at.desc())\
                                .limit(limit).all()
    
    def __repr__(self):
        return f'<Notification {self.title}>'
