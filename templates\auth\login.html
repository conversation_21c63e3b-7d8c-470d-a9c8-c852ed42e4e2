{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة العقود - دولة قطر{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="font-weight-light my-2">
                        <i class="fas fa-file-contract me-2"></i>
                        نظام إدارة العقود - قطر
                    </h3>
                    <p class="mb-0">تسجيل الدخول</p>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="username" name="username" type="text" placeholder="اسم المستخدم" required />
                            <label for="username">اسم المستخدم</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input class="form-control" id="password" name="password" type="password" placeholder="كلمة المرور" required />
                            <label for="password">كلمة المرور</label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" id="remember_me" name="remember_me" type="checkbox" value="1" />
                            <label class="form-check-label" for="remember_me">تذكرني</label>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mt-4 mb-0">
                            <button class="btn btn-primary w-100" type="submit">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small text-muted">
                        <strong>بيانات تجريبية:</strong><br>
                        المدير: admin / admin123<br>
                        الموظف: employee / employee123
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.card {
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-bottom: none;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
}

.form-floating > .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
</style>
{% endblock %}
