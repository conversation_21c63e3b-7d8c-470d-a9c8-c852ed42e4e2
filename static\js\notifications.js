// Notifications JavaScript

let notificationCheckInterval;

$(document).ready(function() {
    // Load notifications on page load
    loadNotifications();
    
    // Check for new notifications every 30 seconds
    notificationCheckInterval = setInterval(loadNotifications, 30000);
    
    // Mark notification as read when clicked
    $(document).on('click', '.notification-item', function() {
        const notificationId = $(this).data('notification-id');
        if (notificationId && !$(this).hasClass('read')) {
            markNotificationAsRead(notificationId);
        }
    });
});

// Load notifications from server
function loadNotifications() {
    $.ajax({
        url: '/api/notifications',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                updateNotificationUI(response.notifications, response.unread_count);
            }
        },
        error: function() {
            console.error('Failed to load notifications');
        }
    });
}

// Update notification UI
function updateNotificationUI(notifications, unreadCount) {
    const badge = $('#notificationBadge');
    const list = $('#notificationsList');
    
    // Update badge
    if (unreadCount > 0) {
        badge.text(unreadCount).show();
    } else {
        badge.hide();
    }
    
    // Update notifications list
    if (notifications.length === 0) {
        list.html(`
            <li class="dropdown-item text-center text-muted">
                <i class="fas fa-bell-slash me-2"></i>
                لا توجد إشعارات
            </li>
        `);
    } else {
        let notificationsHtml = '';
        notifications.forEach(notification => {
            const isRead = notification.is_read;
            const typeIcon = getNotificationIcon(notification.type);
            const typeColor = getNotificationColor(notification.type);
            const timeAgo = getTimeAgo(notification.created_at);
            
            notificationsHtml += `
                <li class="dropdown-item notification-item ${isRead ? 'read' : 'unread'}" 
                    data-notification-id="${notification.id}"
                    ${notification.contract_id ? `onclick="window.location.href='/contracts/view/${notification.contract_id}'"` : ''}>
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-3">
                            <i class="fas ${typeIcon} text-${typeColor}"></i>
                        </div>
                        <div class="notification-content flex-grow-1">
                            <div class="notification-title fw-bold ${isRead ? '' : 'text-primary'}">
                                ${notification.title}
                            </div>
                            <div class="notification-message text-muted small">
                                ${notification.message}
                            </div>
                            <div class="notification-time text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                ${timeAgo}
                            </div>
                        </div>
                        ${!isRead ? '<div class="notification-dot bg-primary rounded-circle"></div>' : ''}
                    </div>
                </li>
            `;
        });
        list.html(notificationsHtml);
    }
}

// Get notification icon based on type
function getNotificationIcon(type) {
    const icons = {
        'info': 'fa-info-circle',
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle'
    };
    return icons[type] || 'fa-bell';
}

// Get notification color based on type
function getNotificationColor(type) {
    const colors = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger'
    };
    return colors[type] || 'secondary';
}

// Get time ago string
function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// Mark notification as read
function markNotificationAsRead(notificationId) {
    $.ajax({
        url: `/api/notifications/${notificationId}/read`,
        type: 'POST',
        success: function(response) {
            if (response.success) {
                // Update UI to show as read
                $(`.notification-item[data-notification-id="${notificationId}"]`)
                    .removeClass('unread')
                    .addClass('read')
                    .find('.notification-title')
                    .removeClass('text-primary');
                
                // Remove unread dot
                $(`.notification-item[data-notification-id="${notificationId}"] .notification-dot`).remove();
                
                // Update badge count
                const currentCount = parseInt($('#notificationBadge').text()) || 0;
                const newCount = Math.max(0, currentCount - 1);
                if (newCount > 0) {
                    $('#notificationBadge').text(newCount);
                } else {
                    $('#notificationBadge').hide();
                }
            }
        },
        error: function() {
            console.error('Failed to mark notification as read');
        }
    });
}

// Mark all notifications as read
function markAllAsRead() {
    $.ajax({
        url: '/api/notifications/mark-all-read',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                // Update UI
                $('.notification-item')
                    .removeClass('unread')
                    .addClass('read')
                    .find('.notification-title')
                    .removeClass('text-primary');
                
                // Remove all unread dots
                $('.notification-dot').remove();
                
                // Hide badge
                $('#notificationBadge').hide();
                
                showAlert('تم تحديد جميع الإشعارات كمقروءة', 'success');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء تحديث الإشعارات', 'error');
        }
    });
}

// Create new notification (for real-time updates)
function createNotification(title, message, type = 'info', contractId = null) {
    $.ajax({
        url: '/api/notifications',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            title: title,
            message: message,
            type: type,
            contract_id: contractId
        }),
        success: function(response) {
            if (response.success) {
                // Reload notifications to show the new one
                loadNotifications();
                
                // Show browser notification if supported
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification(title, {
                        body: message,
                        icon: '/static/images/logo.png',
                        tag: 'contract-notification'
                    });
                }
            }
        },
        error: function() {
            console.error('Failed to create notification');
        }
    });
}

// Request notification permission
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(function(permission) {
            if (permission === 'granted') {
                showAlert('تم تفعيل الإشعارات المتصفح', 'success');
            }
        });
    }
}

// Initialize browser notifications
$(document).ready(function() {
    // Request permission for browser notifications
    setTimeout(requestNotificationPermission, 2000);
});

// Clean up interval when page unloads
$(window).on('beforeunload', function() {
    if (notificationCheckInterval) {
        clearInterval(notificationCheckInterval);
    }
});
