import smtplib
import os
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from models.email_settings import EmailSettings, ContractSendLog
from models import db

class EmailService:
    def __init__(self):
        self.settings = EmailSettings.get_active_settings()
    
    def send_contract_email(self, contract, recipient_email, recipient_name, sent_by_user_id, 
                           include_pdf=True, include_word=False, custom_message=None):
        """Send contract via email"""
        
        if not self.settings:
            return {
                'success': False,
                'message': 'إعدادات البريد الإلكتروني غير مكونة'
            }
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{self.settings.from_name} <{self.settings.from_email}>"
            msg['To'] = recipient_email
            msg['Subject'] = f"عقد رقم {contract.contract_number} - {contract.title}"
            
            # Email body
            body = self._create_email_body(contract, recipient_name, custom_message)
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # Attach files
            if include_pdf:
                pdf_path = self._generate_contract_pdf(contract)
                if pdf_path and os.path.exists(pdf_path):
                    self._attach_file(msg, pdf_path, f"عقد_{contract.contract_number}.pdf")
            
            if include_word:
                word_path = self._generate_contract_word(contract)
                if word_path and os.path.exists(word_path):
                    self._attach_file(msg, word_path, f"عقد_{contract.contract_number}.docx")
            
            # Send email
            server = smtplib.SMTP(self.settings.smtp_server, self.settings.smtp_port)
            
            if self.settings.use_tls:
                server.starttls()
            
            server.login(self.settings.smtp_username, self.settings.smtp_password)
            server.send_message(msg)
            server.quit()
            
            # Log success
            self._log_send_attempt(contract.id, 'email', recipient_email, 'sent', 
                                 sent_by_user_id, 'تم إرسال البريد الإلكتروني بنجاح')
            
            return {
                'success': True,
                'message': f'تم إرسال العقد بنجاح إلى {recipient_email}'
            }
            
        except Exception as e:
            error_message = f'فشل في إرسال البريد الإلكتروني: {str(e)}'
            
            # Log failure
            self._log_send_attempt(contract.id, 'email', recipient_email, 'failed', 
                                 sent_by_user_id, error_message)
            
            return {
                'success': False,
                'message': error_message
            }
    
    def _create_email_body(self, contract, recipient_name, custom_message=None):
        """Create email body HTML"""
        
        default_message = f"""
        <div style="direction: rtl; text-align: right; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; color: white;">
                <h1 style="margin: 0; font-size: 28px;">نظام إدارة العقود الإلكترونية</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px;">دولة قطر</p>
            </div>
            
            <div style="padding: 30px; background-color: #f8f9fa;">
                <h2 style="color: #333; margin-bottom: 20px;">السلام عليكم ورحمة الله وبركاته</h2>
                
                <p style="font-size: 16px; line-height: 1.6; color: #555;">
                    عزيزي/عزيزتي <strong>{recipient_name}</strong>،
                </p>
                
                <p style="font-size: 16px; line-height: 1.6; color: #555;">
                    {custom_message if custom_message else 'نرسل لكم العقد المرفق للمراجعة والتوقيع.'}
                </p>
                
                <div style="background-color: white; padding: 20px; border-radius: 10px; margin: 20px 0; border-right: 4px solid #667eea;">
                    <h3 style="color: #667eea; margin-top: 0;">تفاصيل العقد:</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">رقم العقد:</td>
                            <td style="padding: 8px 0; color: #555;">{contract.contract_number}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">عنوان العقد:</td>
                            <td style="padding: 8px 0; color: #555;">{contract.title}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">تاريخ العقد:</td>
                            <td style="padding: 8px 0; color: #555;">{contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else 'غير محدد'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">الطرف الأول:</td>
                            <td style="padding: 8px 0; color: #555;">{contract.first_party_name}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">الطرف الثاني:</td>
                            <td style="padding: 8px 0; color: #555;">{contract.second_party_name}</td>
                        </tr>
                        {f'<tr><td style="padding: 8px 0; font-weight: bold; color: #333;">قيمة العقد:</td><td style="padding: 8px 0; color: #555;">{contract.contract_value} {contract.currency}</td></tr>' if contract.contract_value else ''}
                    </table>
                </div>
                
                <p style="font-size: 16px; line-height: 1.6; color: #555;">
                    يرجى مراجعة العقد المرفق والتواصل معنا في حالة وجود أي استفسارات.
                </p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <p style="font-size: 14px; color: #777;">
                        تم إنشاء هذا البريد تلقائياً من نظام إدارة العقود الإلكترونية
                    </p>
                    <p style="font-size: 14px; color: #777;">
                        تاريخ الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    </p>
                </div>
            </div>
            
            <div style="background-color: #333; color: white; text-align: center; padding: 20px;">
                <p style="margin: 0; font-size: 14px;">
                    © 2024 نظام إدارة العقود الإلكترونية - دولة قطر
                </p>
            </div>
        </div>
        """
        
        return default_message
    
    def _attach_file(self, msg, file_path, filename):
        """Attach file to email"""
        try:
            with open(file_path, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}',
            )
            msg.attach(part)
        except Exception as e:
            print(f"Error attaching file {file_path}: {e}")
    
    def _generate_contract_pdf(self, contract):
        """Generate PDF for contract"""
        try:
            from utils.pdf_generator import generate_pdf
            return generate_pdf(contract)
        except Exception as e:
            print(f"Error generating PDF: {e}")
            return None
    
    def _generate_contract_word(self, contract):
        """Generate Word document for contract"""
        try:
            from utils.word_generator import generate_word
            return generate_word(contract)
        except Exception as e:
            print(f"Error generating Word document: {e}")
            return None
    
    def _log_send_attempt(self, contract_id, method, recipient, status, sent_by, message):
        """Log send attempt"""
        try:
            log = ContractSendLog(
                contract_id=contract_id,
                send_method=method,
                recipient=recipient,
                status=status,
                sent_by=sent_by,
                message=message
            )
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            print(f"Error logging send attempt: {e}")
            db.session.rollback()
    
    def test_connection(self):
        """Test email connection"""
        if not self.settings:
            return {
                'success': False,
                'message': 'إعدادات البريد الإلكتروني غير مكونة'
            }
        
        try:
            server = smtplib.SMTP(self.settings.smtp_server, self.settings.smtp_port)
            
            if self.settings.use_tls:
                server.starttls()
            
            server.login(self.settings.smtp_username, self.settings.smtp_password)
            server.quit()
            
            return {
                'success': True,
                'message': 'تم الاتصال بخادم البريد الإلكتروني بنجاح'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في الاتصال بخادم البريد الإلكتروني: {str(e)}'
            }
