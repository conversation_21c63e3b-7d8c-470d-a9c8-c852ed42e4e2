import os
import json
import zipfile
import shutil
from datetime import datetime, timedelta
import sqlite3
import schedule
import time
import threading
from pathlib import Path

class BackupService:
    def __init__(self):
        self.backup_dir = os.path.join('backups')
        self.contracts_backup_dir = os.path.join(self.backup_dir, 'contracts')
        self.database_backup_dir = os.path.join(self.backup_dir, 'database')
        self.files_backup_dir = os.path.join(self.backup_dir, 'files')
        
        # Create backup directories
        os.makedirs(self.contracts_backup_dir, exist_ok=True)
        os.makedirs(self.database_backup_dir, exist_ok=True)
        os.makedirs(self.files_backup_dir, exist_ok=True)
        
        self.max_backups = 30  # Keep 30 days of backups
        self.backup_running = False
    
    def create_full_backup(self):
        """Create full system backup"""
        
        if self.backup_running:
            return {
                'success': False,
                'message': 'النسخ الاحتياطي قيد التشغيل بالفعل'
            }
        
        self.backup_running = True
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"full_backup_{timestamp}"
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # Backup database
                db_backup = self._backup_database()
                if db_backup['success']:
                    backup_zip.write(db_backup['file_path'], f"database/{os.path.basename(db_backup['file_path'])}")
                
                # Backup contracts data
                contracts_backup = self._backup_contracts_data()
                if contracts_backup['success']:
                    backup_zip.write(contracts_backup['file_path'], f"contracts/{os.path.basename(contracts_backup['file_path'])}")
                
                # Backup uploaded files
                self._backup_files_to_zip(backup_zip)
                
                # Backup configuration
                config_backup = self._backup_configuration()
                if config_backup['success']:
                    backup_zip.write(config_backup['file_path'], f"config/{os.path.basename(config_backup['file_path'])}")
            
            # Clean old backups
            self._clean_old_backups()
            
            backup_size = os.path.getsize(backup_path)
            
            return {
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_path': backup_path,
                'backup_size': backup_size,
                'timestamp': timestamp
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في إنشاء النسخة الاحتياطية: {str(e)}'
            }
        finally:
            self.backup_running = False
    
    def _backup_database(self):
        """Backup SQLite database"""
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            db_backup_path = os.path.join(self.database_backup_dir, f"database_{timestamp}.db")
            
            # Copy database file
            db_path = 'contracts.db'  # Adjust path as needed
            if os.path.exists(db_path):
                shutil.copy2(db_path, db_backup_path)
                
                return {
                    'success': True,
                    'file_path': db_backup_path
                }
            else:
                return {
                    'success': False,
                    'message': 'ملف قاعدة البيانات غير موجود'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في نسخ قاعدة البيانات: {str(e)}'
            }
    
    def _backup_contracts_data(self):
        """Backup contracts data as JSON"""
        
        try:
            from models.contract import Contract
            from models.contract_type import ContractType
            from models.user import User
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            contracts_backup_path = os.path.join(self.contracts_backup_dir, f"contracts_{timestamp}.json")
            
            # Get all contracts
            contracts = Contract.query.all()
            contract_types = ContractType.query.all()
            users = User.query.all()
            
            backup_data = {
                'backup_timestamp': timestamp,
                'backup_date': datetime.now().isoformat(),
                'contracts': [],
                'contract_types': [],
                'users': []
            }
            
            # Backup contracts
            for contract in contracts:
                contract_data = {
                    'id': contract.id,
                    'contract_number': contract.contract_number,
                    'title': contract.title,
                    'content': contract.content,
                    'first_party_name': contract.first_party_name,
                    'first_party_id': contract.first_party_id,
                    'first_party_phone': contract.first_party_phone,
                    'first_party_address': contract.first_party_address,
                    'second_party_name': contract.second_party_name,
                    'second_party_id': contract.second_party_id,
                    'second_party_phone': contract.second_party_phone,
                    'second_party_address': contract.second_party_address,
                    'contract_type_id': contract.contract_type_id,
                    'contract_date': contract.contract_date.isoformat() if contract.contract_date else None,
                    'start_date': contract.start_date.isoformat() if contract.start_date else None,
                    'end_date': contract.end_date.isoformat() if contract.end_date else None,
                    'contract_value': float(contract.contract_value) if contract.contract_value else None,
                    'currency': contract.currency,
                    'status': contract.status,
                    'terms_conditions': contract.terms_conditions,
                    'created_by': contract.created_by,
                    'created_at': contract.created_at.isoformat() if contract.created_at else None,
                    'updated_at': contract.updated_at.isoformat() if contract.updated_at else None,
                    'first_party_signature': contract.first_party_signature,
                    'second_party_signature': contract.second_party_signature,
                    'company_seal': contract.company_seal
                }
                backup_data['contracts'].append(contract_data)
            
            # Backup contract types
            for contract_type in contract_types:
                type_data = {
                    'id': contract_type.id,
                    'name': contract_type.name,
                    'description': contract_type.description,
                    'template': contract_type.template,
                    'fields': contract_type.fields,
                    'is_active': contract_type.is_active,
                    'created_at': contract_type.created_at.isoformat() if contract_type.created_at else None
                }
                backup_data['contract_types'].append(type_data)
            
            # Backup users (without passwords)
            for user in users:
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'full_name': user.full_name,
                    'role': user.role,
                    'is_active': user.is_active,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                }
                backup_data['users'].append(user_data)
            
            # Save to JSON file
            with open(contracts_backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'file_path': contracts_backup_path
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في نسخ بيانات العقود: {str(e)}'
            }
    
    def _backup_files_to_zip(self, backup_zip):
        """Backup uploaded files to zip"""
        
        try:
            # Backup static files
            static_dirs = ['static/uploads', 'static/exports', 'static/qr_codes']
            
            for static_dir in static_dirs:
                if os.path.exists(static_dir):
                    for root, dirs, files in os.walk(static_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, 'static')
                            backup_zip.write(file_path, f"files/{arcname}")
            
        except Exception as e:
            print(f"Error backing up files: {e}")
    
    def _backup_configuration(self):
        """Backup system configuration"""
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            config_backup_path = os.path.join(self.backup_dir, f"config_{timestamp}.json")
            
            config_data = {
                'backup_timestamp': timestamp,
                'backup_date': datetime.now().isoformat(),
                'system_info': {
                    'python_version': '3.x',
                    'flask_version': '2.x',
                    'database_type': 'SQLite'
                },
                'settings': {
                    'max_backups': self.max_backups,
                    'backup_enabled': True
                }
            }
            
            # Add email settings (without passwords)
            try:
                from models.email_settings import EmailSettings
                email_settings = EmailSettings.get_active_settings()
                if email_settings:
                    config_data['email_settings'] = {
                        'smtp_server': email_settings.smtp_server,
                        'smtp_port': email_settings.smtp_port,
                        'smtp_username': email_settings.smtp_username,
                        'from_email': email_settings.from_email,
                        'from_name': email_settings.from_name,
                        'use_tls': email_settings.use_tls
                    }
            except:
                pass
            
            with open(config_backup_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'file_path': config_backup_path
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في نسخ الإعدادات: {str(e)}'
            }
    
    def _clean_old_backups(self):
        """Clean old backup files"""
        
        try:
            cutoff_date = datetime.now() - timedelta(days=self.max_backups)
            
            for backup_file in os.listdir(self.backup_dir):
                if backup_file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, backup_file)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        print(f"Removed old backup: {backup_file}")
            
        except Exception as e:
            print(f"Error cleaning old backups: {e}")
    
    def restore_from_backup(self, backup_path):
        """Restore system from backup"""
        
        if not os.path.exists(backup_path):
            return {
                'success': False,
                'message': 'ملف النسخة الاحتياطية غير موجود'
            }
        
        try:
            # Create restore directory
            restore_dir = os.path.join(self.backup_dir, 'restore_temp')
            os.makedirs(restore_dir, exist_ok=True)
            
            # Extract backup
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                backup_zip.extractall(restore_dir)
            
            # Restore database
            db_files = [f for f in os.listdir(os.path.join(restore_dir, 'database')) if f.endswith('.db')]
            if db_files:
                latest_db = max(db_files)
                shutil.copy2(os.path.join(restore_dir, 'database', latest_db), 'contracts.db')
            
            # Restore files
            files_dir = os.path.join(restore_dir, 'files')
            if os.path.exists(files_dir):
                for root, dirs, files in os.walk(files_dir):
                    for file in files:
                        src_path = os.path.join(root, file)
                        rel_path = os.path.relpath(src_path, files_dir)
                        dst_path = os.path.join('static', rel_path)
                        
                        os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                        shutil.copy2(src_path, dst_path)
            
            # Clean up
            shutil.rmtree(restore_dir)
            
            return {
                'success': True,
                'message': 'تم استعادة النسخة الاحتياطية بنجاح'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في استعادة النسخة الاحتياطية: {str(e)}'
            }
    
    def get_backup_list(self):
        """Get list of available backups"""
        
        try:
            backups = []
            
            for backup_file in os.listdir(self.backup_dir):
                if backup_file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, backup_file)
                    file_stats = os.stat(file_path)
                    
                    backup_info = {
                        'filename': backup_file,
                        'path': file_path,
                        'size': file_stats.st_size,
                        'created_at': datetime.fromtimestamp(file_stats.st_ctime),
                        'size_mb': round(file_stats.st_size / (1024 * 1024), 2)
                    }
                    backups.append(backup_info)
            
            # Sort by creation date (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
            return {
                'success': True,
                'backups': backups
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في جلب قائمة النسخ الاحتياطية: {str(e)}'
            }
    
    def schedule_automatic_backups(self):
        """Schedule automatic daily backups"""
        
        # Schedule daily backup at 2 AM
        schedule.every().day.at("02:00").do(self.create_full_backup)
        
        # Schedule weekly cleanup
        schedule.every().sunday.at("03:00").do(self._clean_old_backups)
        
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        
        # Run scheduler in background thread
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        return {
            'success': True,
            'message': 'تم تفعيل النسخ الاحتياطي التلقائي'
        }
