# نظام إدارة العقود الإلكترونية
## Electronic Contract Management System

نظام شامل لإدارة العقود الإلكترونية مبني بـ Flask مع دعم كامل للغة العربية والتوقيع الإلكتروني.

A comprehensive electronic contract management system built with Flask, featuring full Arabic language support and electronic signatures.

## ✨ المميزات الرئيسية / Key Features

### 🔐 إدارة المستخدمين / User Management
- تسجيل دخول آمن / Secure login system
- أدوار مختلفة (مدير، موظف) / Different roles (Manager, Employee)
- إدارة الصلاحيات / Permission management

### 📄 إدارة العقود / Contract Management
- إنشاء عقود جديدة / Create new contracts
- أنواع عقود متعددة (بيع، إيجار، تقسيط، إلخ) / Multiple contract types
- محرر نصوص متقدم مع دعم RTL / Advanced text editor with RTL support
- حقول مخصصة لكل نوع عقد / Custom fields for each contract type
- معاينة العقد قبل الحفظ / Contract preview before saving

### ✍️ التوقيع الإلكتروني / Electronic Signatures
- توقيع بالماوس أو اللمس / Mouse or touch signatures
- رفع صور التوقيعات / Upload signature images
- ختم الشركة الإلكتروني / Electronic company seal
- حفظ التوقيعات بصيغة آمنة / Secure signature storage

### 📊 التقارير والإحصائيات / Reports & Analytics
- لوحة تحكم شاملة / Comprehensive dashboard
- إحصائيات العقود / Contract statistics
- تقارير حسب التاريخ والنوع / Reports by date and type
- بحث متقدم / Advanced search

### 📤 التصدير والطباعة / Export & Print
- تصدير PDF / PDF export
- تصدير Word / Word export
- طباعة مباشرة / Direct printing
- تنسيق A4 احترافي / Professional A4 formatting

## 🚀 التثبيت والتشغيل / Installation & Setup

### المتطلبات / Requirements
- Python 3.8+
- Flask 2.3+
- SQLAlchemy 3.0+

### خطوات التثبيت / Installation Steps

1. **استنساخ المشروع / Clone the project**
```bash
git clone <repository-url>
cd contract-management-system
```

2. **تثبيت المتطلبات / Install dependencies**
```bash
pip install -r requirements.txt
```

3. **تشغيل التطبيق / Run the application**
```bash
python app.py
```

4. **فتح المتصفح / Open browser**
```
http://localhost:5000
```

### بيانات الدخول الافتراضية / Default Login Credentials

**المدير / Manager:**
- اسم المستخدم / Username: `admin`
- كلمة المرور / Password: `admin123`

**الموظف / Employee:**
- اسم المستخدم / Username: `employee`
- كلمة المرور / Password: `employee123`

## 📁 هيكل المشروع / Project Structure

```
contract_management_system/
├── app.py                          # التطبيق الرئيسي / Main application
├── config.py                       # إعدادات النظام / System configuration
├── requirements.txt                # المتطلبات / Dependencies
├── models/                         # نماذج قاعدة البيانات / Database models
│   ├── __init__.py
│   ├── user.py                     # نموذج المستخدم / User model
│   ├── contract.py                 # نموذج العقد / Contract model
│   └── contract_type.py            # نموذج نوع العقد / Contract type model
├── routes/                         # مسارات التطبيق / Application routes
│   ├── __init__.py
│   ├── auth.py                     # مسارات المصادقة / Authentication routes
│   ├── dashboard.py                # مسارات لوحة التحكم / Dashboard routes
│   ├── contracts.py                # مسارات العقود / Contract routes
│   └── api.py                      # مسارات API / API routes
├── templates/                      # قوالب HTML / HTML templates
│   ├── base.html                   # القالب الأساسي / Base template
│   ├── auth/                       # قوالب المصادقة / Auth templates
│   ├── dashboard/                  # قوالب لوحة التحكم / Dashboard templates
│   ├── contracts/                  # قوالب العقود / Contract templates
│   └── components/                 # مكونات مشتركة / Shared components
├── static/                         # الملفات الثابتة / Static files
│   ├── css/                        # ملفات CSS / CSS files
│   ├── js/                         # ملفات JavaScript / JavaScript files
│   └── uploads/                    # الملفات المرفوعة / Uploaded files
└── utils/                          # أدوات مساعدة / Utility functions
    ├── pdf_generator.py            # مولد PDF / PDF generator
    ├── word_generator.py           # مولد Word / Word generator
    └── signature_handler.py        # معالج التوقيعات / Signature handler
```

## 🎯 أنواع العقود المدعومة / Supported Contract Types

- **بيع سيارة** / Car Sale
- **شراء عقار** / Property Purchase  
- **إيجار** / Lease
- **تقسيط** / Installment
- **عمل** / Employment
- **خدمات** / Services
- **شراكة** / Partnership
- **أخرى** / Other

## 🔧 المميزات التقنية / Technical Features

### الأمان / Security
- تشفير كلمات المرور / Password encryption
- جلسات آمنة / Secure sessions
- حماية CSRF / CSRF protection
- التحقق من الصلاحيات / Permission validation

### واجهة المستخدم / User Interface
- تصميم متجاوب / Responsive design
- دعم RTL كامل / Full RTL support
- خطوط عربية احترافية / Professional Arabic fonts
- تجربة مستخدم سلسة / Smooth user experience

### قاعدة البيانات / Database
- SQLite للتطوير / SQLite for development
- دعم PostgreSQL/MySQL للإنتاج / PostgreSQL/MySQL support for production
- نسخ احتياطي تلقائي / Automatic backups
- فهرسة محسنة / Optimized indexing

## 🔮 المميزات المستقبلية / Future Features

### 📧 التكامل مع الخدمات الخارجية / External Service Integration
- إرسال العقود عبر البريد الإلكتروني / Email contract sending
- إرسال عبر WhatsApp / WhatsApp integration
- تكامل مع خدمات التوقيع الخارجية / External signature service integration

### 📱 مميزات متقدمة / Advanced Features
- رموز QR في نهاية العقود / QR codes at contract end
- توقيع مباشر بالقلم الرقمي / Live stylus signing
- دعم العقود ثنائية اللغة (عربي/إنجليزي) / Bilingual contracts (Arabic/English)
- نسخ احتياطي تلقائي للسحابة / Automatic cloud backup

### 📊 تحليلات متقدمة / Advanced Analytics
- تقارير مالية تفصيلية / Detailed financial reports
- تحليل أداء العقود / Contract performance analysis
- تنبيهات انتهاء العقود / Contract expiration alerts
- إحصائيات استخدام النظام / System usage statistics

## 🛠️ التطوير والمساهمة / Development & Contributing

### إعداد بيئة التطوير / Development Environment Setup
```bash
# إنشاء بيئة افتراضية / Create virtual environment
python -m venv venv

# تفعيل البيئة الافتراضية / Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# تثبيت المتطلبات / Install dependencies
pip install -r requirements.txt

# تشغيل في وضع التطوير / Run in development mode
python app.py
```

### إرشادات المساهمة / Contributing Guidelines
1. Fork المشروع / Fork the project
2. إنشاء فرع للميزة الجديدة / Create feature branch
3. إضافة التحسينات / Add improvements
4. اختبار التغييرات / Test changes
5. إرسال Pull Request / Submit Pull Request

## 📞 الدعم والمساعدة / Support & Help

للحصول على المساعدة أو الإبلاغ عن مشاكل:
For support or to report issues:

- إنشاء Issue في GitHub / Create GitHub Issue
- مراجعة الوثائق / Check documentation
- التواصل مع فريق التطوير / Contact development team

## 📄 الترخيص / License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
This project is licensed under the MIT License - see the LICENSE file for details.

---

**تم تطويره بـ ❤️ للمجتمع العربي**
**Developed with ❤️ for the Arabic community**
