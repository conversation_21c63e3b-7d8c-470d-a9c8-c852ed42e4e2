#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix script for QR Code and Bilingual features
"""

from app import create_app
from models import db

def fix_qr_directory():
    """Create QR code directory"""
    import os
    qr_dir = os.path.join('static', 'qr_codes')
    os.makedirs(qr_dir, exist_ok=True)
    print(f"✅ Created QR directory: {qr_dir}")

def test_qr_generation():
    """Test QR code generation with a real contract"""
    try:
        from models.contract import Contract
        from utils.qr_service import QRService
        
        # Get first contract
        contract = Contract.query.first()
        if not contract:
            print("❌ No contracts found for testing")
            return False
        
        qr_service = QRService()
        result = qr_service.generate_contract_qr(contract, include_verification=True)
        
        if result['success']:
            print(f"✅ QR Code generated successfully for contract {contract.contract_number}")
            print(f"   Base64 length: {len(result['base64'])}")
            return True
        else:
            print(f"❌ QR Code generation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ QR Code test failed: {e}")
        return False

def test_bilingual_generation():
    """Test bilingual contract generation with a real contract"""
    try:
        from models.contract import Contract
        from utils.bilingual_service import BilingualService
        
        # Get first contract
        contract = Contract.query.first()
        if not contract:
            print("❌ No contracts found for testing")
            return False
        
        bilingual_service = BilingualService()
        result = bilingual_service.create_bilingual_contract(contract, include_english=True)
        
        if result['success']:
            print(f"✅ Bilingual contract generated successfully for contract {contract.contract_number}")
            print(f"   Content length: {len(result['content'])}")
            return True
        else:
            print(f"❌ Bilingual contract generation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Bilingual contract test failed: {e}")
        return False

def create_sample_email_settings():
    """Create sample email settings"""
    try:
        from models.email_settings import EmailSettings
        
        # Check if settings already exist
        existing = EmailSettings.query.first()
        if existing:
            print("✅ Email settings already exist")
            return True
        
        # Create sample settings
        settings = EmailSettings(
            smtp_server='smtp.gmail.com',
            smtp_port=587,
            smtp_username='<EMAIL>',
            smtp_password='your-app-password',
            from_email='<EMAIL>',
            from_name='نظام إدارة العقود',
            use_tls=True
        )
        
        db.session.add(settings)
        db.session.commit()
        
        print("✅ Sample email settings created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create email settings: {e}")
        return False

def create_sample_whatsapp_settings():
    """Create sample WhatsApp settings"""
    try:
        from models.email_settings import WhatsAppSettings
        
        # Check if settings already exist
        existing = WhatsAppSettings.query.first()
        if existing:
            print("✅ WhatsApp settings already exist")
            return True
        
        # Create sample settings
        settings = WhatsAppSettings(
            api_url='https://graph.facebook.com/v17.0',
            api_token='your-api-token',
            phone_number_id='your-phone-number-id',
            business_account_id='your-business-account-id'
        )
        
        db.session.add(settings)
        db.session.commit()
        
        print("✅ Sample WhatsApp settings created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create WhatsApp settings: {e}")
        return False

def main():
    """Main fix function"""
    print("🔧 Running fix script for advanced features...")
    print("=" * 60)
    
    app = create_app()
    with app.app_context():
        # Fix QR directory
        fix_qr_directory()
        print()
        
        # Create database tables
        try:
            db.create_all()
            print("✅ Database tables created/updated")
        except Exception as e:
            print(f"❌ Database error: {e}")
        print()
        
        # Create sample settings
        create_sample_email_settings()
        create_sample_whatsapp_settings()
        print()
        
        # Test features
        print("🧪 Testing features...")
        qr_success = test_qr_generation()
        bilingual_success = test_bilingual_generation()
        print()
        
        if qr_success and bilingual_success:
            print("🎉 All features are working correctly!")
        else:
            print("⚠️ Some features need attention:")
            if not qr_success:
                print("   - QR Code generation needs fixing")
            if not bilingual_success:
                print("   - Bilingual contract generation needs fixing")
        
        print("\n📝 Next steps:")
        print("1. Configure email settings in the admin panel")
        print("2. Configure WhatsApp Business API settings")
        print("3. Test the features in the web interface")
        print("4. Check the /verify page for QR code verification")

if __name__ == '__main__':
    main()
