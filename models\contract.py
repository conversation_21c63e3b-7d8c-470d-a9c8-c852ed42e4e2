from datetime import datetime
from sqlalchemy import Numeric
from . import db

class Contract(db.Model):
    __tablename__ = 'contracts'
    
    id = db.Column(db.Integer, primary_key=True)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    contract_type_id = db.Column(db.Integer, db.ForeignKey('contract_types.id'), nullable=False)
    
    # Parties information
    first_party_name = db.Column(db.String(200), nullable=False)
    first_party_id = db.Column(db.String(50))
    first_party_address = db.Column(db.Text)
    first_party_phone = db.Column(db.String(20))
    
    second_party_name = db.Column(db.String(200), nullable=False)
    second_party_id = db.Column(db.String(50))
    second_party_address = db.Column(db.Text)
    second_party_phone = db.Column(db.String(20))
    
    # Contract content
    content = db.Column(db.Text, nullable=False)  # HTML content
    terms_conditions = db.Column(db.Text)
    contract_value = db.Column(Numeric(15, 2))
    currency = db.Column(db.String(10), default='QAR')
    
    # Contract dates
    contract_date = db.Column(db.Date, nullable=False)
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    
    # Status and metadata
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, cancelled
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Signatures
    first_party_signature = db.Column(db.Text)  # Base64 encoded signature
    second_party_signature = db.Column(db.Text)  # Base64 encoded signature
    company_seal = db.Column(db.Text)  # Base64 encoded seal
    
    # Additional data
    custom_fields = db.Column(db.JSON)  # Store custom field values
    attachments = db.Column(db.JSON)  # Store file paths
    
    # Relationships
    contract_type = db.relationship('ContractType', back_populates='contracts')
    
    def __init__(self, title, contract_type_id, first_party_name, second_party_name, 
                 content, contract_date, created_by, **kwargs):
        self.title = title
        self.contract_type_id = contract_type_id
        self.first_party_name = first_party_name
        self.second_party_name = second_party_name
        self.content = content
        self.contract_date = contract_date
        self.created_by = created_by
        self.contract_number = self.generate_contract_number()
        
        # Set optional fields
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def generate_contract_number(self):
        """Generate unique contract number"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f"CON-{timestamp}"
    
    def get_status_display(self):
        """Get human-readable status"""
        status_map = {
            'draft': 'مسودة',
            'active': 'نشط',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(self.status, self.status)
    
    def is_signed(self):
        """Check if contract is fully signed"""
        return (self.first_party_signature and 
                self.second_party_signature and 
                self.company_seal)
    
    def can_edit(self, user):
        """Check if user can edit this contract"""
        return (user.is_manager() or 
                user.id == self.created_by or 
                self.status == 'draft')
    
    def to_dict(self):
        """Convert contract to dictionary"""
        return {
            'id': self.id,
            'contract_number': self.contract_number,
            'title': self.title,
            'contract_type_id': self.contract_type_id,
            'first_party_name': self.first_party_name,
            'second_party_name': self.second_party_name,
            'contract_value': float(self.contract_value) if self.contract_value else None,
            'currency': self.currency,
            'contract_date': self.contract_date.isoformat() if self.contract_date else None,
            'status': self.status,
            'status_display': self.get_status_display(),
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_signed': self.is_signed(),
            'custom_fields': self.custom_fields,
            'attachments': self.attachments
        }
    
    def __repr__(self):
        return f'<Contract {self.contract_number}>'
