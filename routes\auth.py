from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from models import db
from models.user import User

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = bool(request.form.get('remember_me'))
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user, remember=remember_me)
            user.update_last_login()
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('dashboard.index')
            
            flash(f'مرحباً {user.full_name}', 'success')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    """Register new user (managers only)"""
    if not current_user.is_manager():
        flash('غير مصرح لك بإنشاء مستخدمين جدد', 'error')
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        full_name = request.form.get('full_name')
        role = request.form.get('role', 'employee')
        
        # Validation
        if not all([username, email, password, full_name]):
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('auth/register.html')
        
        # Check if user exists
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('auth/register.html')
        
        # Create new user
        try:
            user = User(
                username=username,
                email=email,
                password=password,
                full_name=full_name,
                role=role
            )
            db.session.add(user)
            db.session.commit()
            
            flash(f'تم إنشاء المستخدم {full_name} بنجاح', 'success')
            return redirect(url_for('dashboard.users'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المستخدم', 'error')
    
    return render_template('auth/register.html')

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile page"""
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        
        if not full_name or not email:
            flash('الاسم والبريد الإلكتروني مطلوبان', 'error')
            return render_template('auth/profile.html')
        
        # Update basic info
        current_user.full_name = full_name
        current_user.email = email
        
        # Update password if provided
        if new_password:
            if not current_password or not current_user.check_password(current_password):
                flash('كلمة المرور الحالية غير صحيحة', 'error')
                return render_template('auth/profile.html')
            
            current_user.set_password(new_password)
        
        try:
            db.session.commit()
            flash('تم تحديث الملف الشخصي بنجاح', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء التحديث', 'error')
    
    return render_template('auth/profile.html')
