# 🔧 دليل استكشاف الأخطاء وإصلاحها

## 🚨 المشاكل الشائعة والحلول

### 1. ❌ خطأ "حدث خطأ أثناء إنشاء QR Code"

#### الأسباب المحتملة:
- مكتبة qrcode غير مثبتة
- مشكلة في صلاحيات الملفات
- خطأ في استيراد المكتبات

#### الحلول:
```bash
# 1. تثبيت مكتبة QR Code
pip install qrcode[pil]

# 2. تحقق من التثبيت
python -c "import qrcode; print('QR Code library installed successfully')"

# 3. إنشاء مجلد QR codes
mkdir -p static/qr_codes

# 4. تشغيل اختبار QR Code
python test_qr.py
```

#### اختبار سريع:
1. اذهب إلى: `http://localhost:5000/test-api`
2. ان<PERSON><PERSON> على "تحميل العقود"
3. انقر على "اختبار QR Code"
4. تحقق من النتيجة

### 2. ❌ خطأ "حدث خطأ أثناء إنشاء العقد ثنائي اللغة"

#### الأسباب المحتملة:
- خطأ في تحويل التاريخ الهجري
- مشكلة في الترجمة
- بيانات العقد ناقصة

#### الحلول:
```bash
# 1. تشغيل اختبار الخدمة
python debug_services.py

# 2. تحقق من بيانات العقد
python -c "
from app import create_app
from models.contract import Contract
app = create_app()
with app.app_context():
    contract = Contract.query.first()
    print(f'Contract: {contract.contract_number if contract else \"No contracts found\"}')
"
```

#### اختبار سريع:
1. اذهب إلى: `http://localhost:5000/test-api`
2. انقر على "اختبار ثنائي اللغة"
3. تحقق من النتيجة

### 3. 🔐 مشاكل تسجيل الدخول في API

#### الأعراض:
- رسالة "يجب تسجيل الدخول أولاً"
- إعادة توجيه إلى صفحة تسجيل الدخول

#### الحلول:
1. **تأكد من تسجيل الدخول:**
   - اذهب إلى `http://localhost:5000`
   - سجل الدخول بـ: admin / admin123
   - ثم جرب المميزات

2. **تحقق من الجلسة:**
   - افتح Developer Tools (F12)
   - تحقق من وجود cookies
   - تحقق من عدم انتهاء الجلسة

3. **مسح cache المتصفح:**
   - Ctrl+Shift+Delete
   - امسح cookies والبيانات المحفوظة

### 4. 📧 مشاكل إرسال البريد الإلكتروني

#### الأعراض:
- "خدمة البريد الإلكتروني غير متوفرة"
- فشل في الإرسال

#### الحلول:
1. **كون إعدادات البريد:**
   - اذهب إلى "إعدادات النظام" → "البريد الإلكتروني"
   - أدخل إعدادات SMTP صحيحة

2. **لـ Gmail:**
   ```
   SMTP Server: smtp.gmail.com
   Port: 587
   Username: <EMAIL>
   Password: your-app-password (ليس كلمة المرور العادية)
   Use TLS: ✓
   ```

3. **إنشاء App Password:**
   - اذهب إلى Google Account Settings
   - Security → 2-Step Verification
   - App passwords → Generate

### 5. 📱 مشاكل WhatsApp

#### الأعراض:
- "خدمة WhatsApp غير متوفرة"
- فشل في الإرسال

#### الحلول:
1. **احصل على WhatsApp Business API:**
   - تحتاج حساب Facebook Business
   - تحتاج WhatsApp Business API (مدفوع)

2. **كون الإعدادات:**
   - اذهب إلى "إعدادات النظام" → "WhatsApp"
   - أدخل بيانات API

3. **للاختبار بدون API:**
   - يمكنك تعطيل الميزة مؤقتاً
   - أو استخدام خدمة WhatsApp مجانية أخرى

### 6. 💾 مشاكل النسخ الاحتياطي

#### الأعراض:
- فشل في إنشاء النسخة الاحتياطية
- مساحة غير كافية

#### الحلول:
```bash
# 1. تحقق من المساحة
df -h

# 2. إنشاء مجلد النسخ الاحتياطي
mkdir -p backups

# 3. تحقق من الصلاحيات
chmod 755 backups

# 4. اختبار النسخ الاحتياطي
python fix_features.py
```

## 🛠️ أدوات التشخيص

### 1. اختبار شامل للمميزات:
```bash
python debug_services.py
```

### 2. اختبار QR Code فقط:
```bash
python test_qr.py
```

### 3. إصلاح المشاكل:
```bash
python fix_features.py
```

### 4. اختبار API من المتصفح:
```
http://localhost:5000/test-api
```

### 5. صفحة التحقق من QR:
```
http://localhost:5000/verify
```

## 🔍 فحص السجلات

### 1. سجلات Python:
- تحقق من terminal حيث يعمل `python app.py`
- ابحث عن رسائل الخطأ

### 2. سجلات المتصفح:
- اضغط F12
- تبويب Console
- ابحث عن أخطاء JavaScript

### 3. سجلات الشبكة:
- F12 → Network tab
- تحقق من طلبات API
- ابحث عن أخطاء 404, 500, etc.

## 📋 قائمة التحقق السريع

### ✅ قبل الاستخدام:
- [ ] تم تثبيت جميع المكتبات: `pip install -r requirements.txt`
- [ ] تم إنشاء قاعدة البيانات: `python app.py` (أول مرة)
- [ ] تم تسجيل الدخول في المتصفح
- [ ] تم إنشاء عقد واحد على الأقل للاختبار

### ✅ اختبار المميزات:
- [ ] QR Code: `http://localhost:5000/test-api`
- [ ] ثنائي اللغة: `http://localhost:5000/test-api`
- [ ] التحقق: `http://localhost:5000/verify`
- [ ] الإشعارات: أيقونة الجرس في شريط التنقل

### ✅ الإعدادات:
- [ ] البريد الإلكتروني: إعدادات النظام → البريد الإلكتروني
- [ ] WhatsApp: إعدادات النظام → WhatsApp (اختياري)
- [ ] النسخ الاحتياطي: إعدادات النظام → النسخ الاحتياطي

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **تشغيل التشخيص الشامل:**
   ```bash
   python debug_services.py > debug_output.txt 2>&1
   ```

2. **جمع معلومات النظام:**
   ```bash
   python --version
   pip list | grep -E "(qrcode|PIL|requests|schedule)"
   ```

3. **تحقق من سجلات التطبيق:**
   - انسخ رسائل الخطأ من terminal
   - انسخ أخطاء JavaScript من المتصفح

4. **معلومات مفيدة للدعم:**
   - نظام التشغيل
   - إصدار Python
   - رسائل الخطأ الكاملة
   - خطوات إعادة إنتاج المشكلة

---

**💡 نصيحة:** معظم المشاكل تُحل بإعادة تثبيت المكتبات وإعادة تشغيل التطبيق!
