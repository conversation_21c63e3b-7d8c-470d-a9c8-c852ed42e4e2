// Main JavaScript file for Contract Management System

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Confirm delete actions
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var message = $(this).data('message') || 'هل أنت متأكد من الحذف؟';
        
        if (confirm(message)) {
            window.location.href = url;
        }
    });
    
    // Loading spinner for forms
    $('form').on('submit', function() {
        showLoading();
    });
    
    // File upload handling
    initFileUpload();
    
    // Search functionality
    initSearch();
    
    // Contract status update
    initStatusUpdate();
});

// Show loading spinner
function showLoading() {
    $('.loading-overlay').show();
    $('.loading-spinner').show();
}

// Hide loading spinner
function hideLoading() {
    $('.loading-overlay').hide();
    $('.loading-spinner').hide();
}

// Initialize file upload
function initFileUpload() {
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        var files = e.originalEvent.dataTransfer.files;
        handleFileUpload(files);
    });
    
    $('.file-upload-area').on('click', function() {
        $('#file-input').click();
    });
    
    $('#file-input').on('change', function() {
        var files = this.files;
        handleFileUpload(files);
    });
}

// Handle file upload
function handleFileUpload(files) {
    if (files.length > 0) {
        var formData = new FormData();
        formData.append('file', files[0]);
        
        // Get contract ID from URL or form
        var contractId = getContractId();
        
        if (contractId) {
            $.ajax({
                url: '/api/contracts/' + contractId + '/attachments',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showAlert('تم رفع الملف بنجاح', 'success');
                        location.reload();
                    } else {
                        showAlert(response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء رفع الملف', 'error');
                }
            });
        }
    }
}

// Initialize search functionality
function initSearch() {
    $('#search-input').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            searchContracts(query);
        } else {
            $('#search-results').hide();
        }
    });
    
    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#search-container').length) {
            $('#search-results').hide();
        }
    });
}

// Search contracts via API
function searchContracts(query) {
    $.ajax({
        url: '/api/contracts/search',
        type: 'GET',
        data: { q: query, limit: 5 },
        success: function(contracts) {
            displaySearchResults(contracts);
        },
        error: function() {
            console.error('Search failed');
        }
    });
}

// Display search results
function displaySearchResults(contracts) {
    var resultsHtml = '';
    
    if (contracts.length > 0) {
        contracts.forEach(function(contract) {
            resultsHtml += `
                <div class="search-result-item" onclick="window.location.href='/contracts/view/${contract.id}'">
                    <div class="fw-bold">${contract.title}</div>
                    <div class="text-muted small">${contract.contract_number} - ${contract.first_party_name}</div>
                </div>
            `;
        });
    } else {
        resultsHtml = '<div class="text-center text-muted p-3">لا توجد نتائج</div>';
    }
    
    $('#search-results').html(resultsHtml).show();
}

// Initialize status update
function initStatusUpdate() {
    $('.status-select').on('change', function() {
        var contractId = $(this).data('contract-id');
        var newStatus = $(this).val();
        
        updateContractStatus(contractId, newStatus);
    });
}

// Update contract status
function updateContractStatus(contractId, status) {
    $.ajax({
        url: '/contracts/update_status/' + contractId,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ status: status }),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء تحديث الحالة', 'error');
        }
    });
}

// Show alert message
function showAlert(message, type) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert alert at the top of the main content
    $('main').prepend(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Get contract ID from URL or form
function getContractId() {
    var path = window.location.pathname;
    var matches = path.match(/\/contracts\/(?:view|edit)\/(\d+)/);
    if (matches) {
        return matches[1];
    }
    
    // Try to get from form data
    var contractIdInput = $('input[name="contract_id"]');
    if (contractIdInput.length) {
        return contractIdInput.val();
    }
    
    return null;
}

// Format currency
function formatCurrency(amount, currency = 'QAR') {
    const localeMap = {
        'QAR': 'ar-QA',
        'SAR': 'ar-SA',
        'AED': 'ar-AE',
        'KWD': 'ar-KW',
        'BHD': 'ar-BH',
        'USD': 'en-US',
        'EUR': 'en-EU'
    };

    const locale = localeMap[currency] || 'ar-QA';

    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// Print contract
function printContract() {
    window.print();
}

// Export contract
function exportContract(contractId, format) {
    showLoading();
    window.location.href = `/contracts/export/${contractId}/${format}`;
    setTimeout(hideLoading, 2000);
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('تم النسخ إلى الحافظة', 'success');
    }).catch(function() {
        showAlert('فشل في النسخ', 'error');
    });
}

// Validate form
function validateForm(formId) {
    var form = document.getElementById(formId);
    var isValid = true;
    
    // Check required fields
    $(form).find('[required]').each(function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    return isValid;
}

// Auto-save functionality
function initAutoSave(formId, saveUrl) {
    var form = $('#' + formId);
    var saveTimer;
    
    form.find('input, textarea, select').on('input change', function() {
        clearTimeout(saveTimer);
        saveTimer = setTimeout(function() {
            autoSave(form, saveUrl);
        }, 2000); // Save after 2 seconds of inactivity
    });
}

// Auto-save form data
function autoSave(form, saveUrl) {
    var formData = form.serialize();
    
    $.ajax({
        url: saveUrl,
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                showAlert('تم الحفظ التلقائي', 'info');
            }
        },
        error: function() {
            console.error('Auto-save failed');
        }
    });
}
