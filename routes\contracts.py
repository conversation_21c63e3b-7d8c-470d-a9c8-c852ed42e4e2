from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime, date
import json
import os
from models import db
from models.contract import Contract
from models.contract_type import ContractType
from utils.pdf_generator import generate_pdf
from utils.word_generator import generate_word

contracts_bp = Blueprint('contracts', __name__, url_prefix='/contracts')

@contracts_bp.route('/')
@login_required
def index():
    """List all contracts"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # Filter by user role
    if current_user.is_manager():
        contracts_query = Contract.query
    else:
        contracts_query = Contract.query.filter_by(created_by=current_user.id)
    
    contracts = contracts_query.order_by(Contract.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('contracts/index.html', contracts=contracts)

@contracts_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create new contract"""
    if request.method == 'POST':
        try:
            # Get form data
            title = request.form.get('title')
            contract_type_id = request.form.get('contract_type_id', type=int)
            first_party_name = request.form.get('first_party_name')
            second_party_name = request.form.get('second_party_name')
            content = request.form.get('content')
            contract_date_str = request.form.get('contract_date')
            
            # Validation
            if not all([title, contract_type_id, first_party_name, second_party_name, content, contract_date_str]):
                flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
                return render_template('contracts/create.html', 
                                     contract_types=ContractType.query.filter_by(is_active=True).all())
            
            # Parse date
            contract_date = datetime.strptime(contract_date_str, '%Y-%m-%d').date()
            
            # Create contract
            contract = Contract(
                title=title,
                contract_type_id=contract_type_id,
                first_party_name=first_party_name,
                second_party_name=second_party_name,
                content=content,
                contract_date=contract_date,
                created_by=current_user.id,
                first_party_id=request.form.get('first_party_id'),
                first_party_address=request.form.get('first_party_address'),
                first_party_phone=request.form.get('first_party_phone'),
                second_party_id=request.form.get('second_party_id'),
                second_party_address=request.form.get('second_party_address'),
                second_party_phone=request.form.get('second_party_phone'),
                terms_conditions=request.form.get('terms_conditions'),
                contract_value=request.form.get('contract_value', type=float),
                currency=request.form.get('currency', 'SAR'),
                start_date=datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date() if request.form.get('start_date') else None,
                end_date=datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date() if request.form.get('end_date') else None
            )
            
            # Handle custom fields
            custom_fields = {}
            for key, value in request.form.items():
                if key.startswith('custom_'):
                    field_name = key.replace('custom_', '')
                    custom_fields[field_name] = value
            
            if custom_fields:
                contract.custom_fields = custom_fields
            
            db.session.add(contract)
            db.session.commit()
            
            flash(f'تم إنشاء العقد {contract.contract_number} بنجاح', 'success')
            return redirect(url_for('contracts.view', id=contract.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء العقد', 'error')
            print(f"Error creating contract: {e}")
    
    # GET request - show form
    contract_types = ContractType.query.filter_by(is_active=True).all()
    return render_template('contracts/create.html', contract_types=contract_types)

@contracts_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit existing contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        flash('غير مصرح لك بتعديل هذا العقد', 'error')
        return redirect(url_for('contracts.view', id=id))
    
    if request.method == 'POST':
        try:
            # Update contract fields
            contract.title = request.form.get('title')
            contract.first_party_name = request.form.get('first_party_name')
            contract.second_party_name = request.form.get('second_party_name')
            contract.content = request.form.get('content')
            contract.contract_date = datetime.strptime(request.form.get('contract_date'), '%Y-%m-%d').date()
            
            # Update optional fields
            contract.first_party_id = request.form.get('first_party_id')
            contract.first_party_address = request.form.get('first_party_address')
            contract.first_party_phone = request.form.get('first_party_phone')
            contract.second_party_id = request.form.get('second_party_id')
            contract.second_party_address = request.form.get('second_party_address')
            contract.second_party_phone = request.form.get('second_party_phone')
            contract.terms_conditions = request.form.get('terms_conditions')
            contract.contract_value = request.form.get('contract_value', type=float)
            contract.currency = request.form.get('currency', 'SAR')
            
            # Update dates
            if request.form.get('start_date'):
                contract.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            if request.form.get('end_date'):
                contract.end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
            
            # Update custom fields
            custom_fields = {}
            for key, value in request.form.items():
                if key.startswith('custom_'):
                    field_name = key.replace('custom_', '')
                    custom_fields[field_name] = value
            
            if custom_fields:
                contract.custom_fields = custom_fields
            
            db.session.commit()
            flash('تم تحديث العقد بنجاح', 'success')
            return redirect(url_for('contracts.view', id=id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث العقد', 'error')
            print(f"Error updating contract: {e}")
    
    contract_types = ContractType.query.filter_by(is_active=True).all()
    return render_template('contracts/edit.html', contract=contract, contract_types=contract_types)

@contracts_bp.route('/view/<int:id>')
@login_required
def view(id):
    """View contract details"""
    contract = Contract.query.get_or_404(id)

    # Check permissions
    if not current_user.is_manager() and contract.created_by != current_user.id:
        flash('غير مصرح لك بعرض هذا العقد', 'error')
        return redirect(url_for('contracts.index'))

    return render_template('contracts/view.html', contract=contract)

@contracts_bp.route('/archive')
@login_required
def archive():
    """View archived contracts"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # Filter by user role
    if current_user.is_manager():
        contracts_query = Contract.query
    else:
        contracts_query = Contract.query.filter_by(created_by=current_user.id)

    contracts = contracts_query.order_by(Contract.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('contracts/archive.html', contracts=contracts)

@contracts_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """Delete contract (managers only)"""
    if not current_user.is_manager():
        flash('غير مصرح لك بحذف العقود', 'error')
        return redirect(url_for('contracts.view', id=id))

    contract = Contract.query.get_or_404(id)

    try:
        db.session.delete(contract)
        db.session.commit()
        flash('تم حذف العقد بنجاح', 'success')
        return redirect(url_for('contracts.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف العقد', 'error')
        return redirect(url_for('contracts.view', id=id))

@contracts_bp.route('/export/<int:id>/<format>')
@login_required
def export(id, format):
    """Export contract to PDF or Word"""
    contract = Contract.query.get_or_404(id)

    # Check permissions
    if not current_user.is_manager() and contract.created_by != current_user.id:
        flash('غير مصرح لك بتصدير هذا العقد', 'error')
        return redirect(url_for('contracts.view', id=id))

    try:
        if format.lower() == 'pdf':
            file_path = generate_pdf(contract)
            return send_file(file_path, as_attachment=True,
                           download_name=f'{contract.contract_number}.pdf')
        elif format.lower() == 'word':
            file_path = generate_word(contract)
            return send_file(file_path, as_attachment=True,
                           download_name=f'{contract.contract_number}.docx')
        else:
            flash('تنسيق التصدير غير مدعوم', 'error')
            return redirect(url_for('contracts.view', id=id))
    except Exception as e:
        flash('حدث خطأ أثناء تصدير العقد', 'error')
        print(f"Export error: {e}")
        return redirect(url_for('contracts.view', id=id))

@contracts_bp.route('/update_status/<int:id>', methods=['POST'])
@login_required
def update_status(id):
    """Update contract status"""
    contract = Contract.query.get_or_404(id)

    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتعديل هذا العقد'})

    new_status = request.json.get('status')
    valid_statuses = ['draft', 'active', 'completed', 'cancelled']

    if new_status not in valid_statuses:
        return jsonify({'success': False, 'message': 'حالة غير صحيحة'})

    try:
        contract.status = new_status
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم تحديث حالة العقد'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء التحديث'})
