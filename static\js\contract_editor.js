// Contract Editor JavaScript

let quill;

$(document).ready(function() {
    // Initialize Quill editor
    initializeEditor();
    
    // Set default date to today
    $('#contract_date').val(new Date().toISOString().split('T')[0]);
    
    // Handle contract type change
    $('#contract_type_id').on('change', function() {
        loadContractTemplate();
        loadCustomFields();
    });
    
    // Form submission
    $('#contract-form').on('submit', function(e) {
        // Update hidden content field with Quill content
        $('#content').val(quill.root.innerHTML);
        
        // Validate form
        if (!validateContractForm()) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-save functionality
    initAutoSave();
});

// Initialize Quill editor
function initializeEditor() {
    var toolbarOptions = [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image']
    ];

    quill = new Quill('#editor', {
        theme: 'snow',
        modules: {
            toolbar: toolbarOptions
        },
        placeholder: 'أدخل نص العقد هنا...',
        direction: 'rtl'
    });
    
    // Set RTL direction
    quill.format('direction', 'rtl');
    quill.format('align', 'right');
}

// Load contract template based on selected type
function loadContractTemplate() {
    var selectedOption = $('#contract_type_id option:selected');
    var template = selectedOption.data('template');
    
    if (template) {
        quill.root.innerHTML = template;
    }
}

// Load custom fields based on contract type
function loadCustomFields() {
    var selectedOption = $('#contract_type_id option:selected');
    var fields = selectedOption.data('fields');
    
    if (fields && Object.keys(fields).length > 0) {
        var fieldsHtml = '';
        
        Object.keys(fields).forEach(function(fieldKey) {
            var fieldLabel = fields[fieldKey];
            fieldsHtml += `
                <div class="mb-3">
                    <label for="custom_${fieldKey}" class="form-label">${fieldLabel}</label>
                    <input type="text" class="form-control" id="custom_${fieldKey}" name="custom_${fieldKey}">
                </div>
            `;
        });
        
        $('#custom-fields-container').html(fieldsHtml);
        $('#custom-fields-card').show();
    } else {
        $('#custom-fields-card').hide();
    }
}

// Validate contract form
function validateContractForm() {
    var isValid = true;
    var errors = [];
    
    // Check required fields
    var requiredFields = [
        { field: '#title', message: 'عنوان العقد مطلوب' },
        { field: '#contract_type_id', message: 'نوع العقد مطلوب' },
        { field: '#first_party_name', message: 'اسم الطرف الأول مطلوب' },
        { field: '#second_party_name', message: 'اسم الطرف الثاني مطلوب' },
        { field: '#contract_date', message: 'تاريخ العقد مطلوب' }
    ];
    
    requiredFields.forEach(function(item) {
        var value = $(item.field).val();
        if (!value || value.trim() === '') {
            $(item.field).addClass('is-invalid');
            errors.push(item.message);
            isValid = false;
        } else {
            $(item.field).removeClass('is-invalid');
        }
    });
    
    // Check Quill content
    var content = quill.root.innerHTML;
    if (!content || content.trim() === '<p><br></p>' || content.trim() === '') {
        $('.ql-editor').addClass('is-invalid');
        errors.push('محتوى العقد مطلوب');
        isValid = false;
    } else {
        $('.ql-editor').removeClass('is-invalid');
    }
    
    // Validate dates
    var contractDate = new Date($('#contract_date').val());
    var startDate = $('#start_date').val() ? new Date($('#start_date').val()) : null;
    var endDate = $('#end_date').val() ? new Date($('#end_date').val()) : null;
    
    if (startDate && startDate < contractDate) {
        $('#start_date').addClass('is-invalid');
        errors.push('تاريخ البداية لا يمكن أن يكون قبل تاريخ العقد');
        isValid = false;
    }
    
    if (endDate && startDate && endDate < startDate) {
        $('#end_date').addClass('is-invalid');
        errors.push('تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية');
        isValid = false;
    }
    
    // Show errors if any
    if (!isValid) {
        var errorMessage = 'يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n');
        alert(errorMessage);
    }
    
    return isValid;
}

// Preview contract
function previewContract() {
    // Update content from Quill
    $('#content').val(quill.root.innerHTML);
    
    // Collect form data
    var formData = {
        title: $('#title').val(),
        contract_type_id: $('#contract_type_id').val(),
        first_party_name: $('#first_party_name').val(),
        second_party_name: $('#second_party_name').val(),
        content: quill.root.innerHTML,
        contract_date: $('#contract_date').val(),
        contract_value: $('#contract_value').val(),
        currency: $('#currency').val(),
        terms_conditions: $('#terms_conditions').val()
    };
    
    // Add custom fields
    $('[name^="custom_"]').each(function() {
        var fieldName = $(this).attr('name');
        formData[fieldName] = $(this).val();
    });
    
    // Generate preview
    var previewHtml = generatePreviewHtml(formData);
    $('#preview-content').html(previewHtml);
    $('#previewModal').modal('show');
}

// Generate preview HTML
function generatePreviewHtml(data) {
    var content = data.content || '';
    
    // Replace variables in content
    var variables = {
        'date': data.contract_date || '',
        'first_party_name': data.first_party_name || '',
        'second_party_name': data.second_party_name || '',
        'contract_number': 'CON-PREVIEW',
        'contract_value': data.contract_value || '',
        'currency': data.currency || 'SAR'
    };
    
    // Add custom fields to variables
    Object.keys(data).forEach(function(key) {
        if (key.startsWith('custom_')) {
            var fieldName = key.replace('custom_', '');
            variables[fieldName] = data[key];
        }
    });
    
    // Replace variables in content
    Object.keys(variables).forEach(function(key) {
        var regex = new RegExp('{{' + key + '}}', 'g');
        content = content.replace(regex, variables[key]);
    });
    
    var previewHtml = `
        <div class="contract-preview">
            <div class="text-center mb-4">
                <h2>${data.title || 'عنوان العقد'}</h2>
                <p class="text-muted">رقم العقد: CON-PREVIEW</p>
                <p class="text-muted">تاريخ العقد: ${data.contract_date || ''}</p>
            </div>
            
            <div class="parties-info mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <h5>الطرف الأول:</h5>
                        <p><strong>الاسم:</strong> ${data.first_party_name || ''}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>الطرف الثاني:</h5>
                        <p><strong>الاسم:</strong> ${data.second_party_name || ''}</p>
                    </div>
                </div>
            </div>
            
            <div class="contract-content mb-4">
                ${content}
            </div>
            
            ${data.contract_value ? `
                <div class="contract-value mb-4">
                    <h5>قيمة العقد:</h5>
                    <p>${data.contract_value} ${data.currency}</p>
                </div>
            ` : ''}
            
            ${data.terms_conditions ? `
                <div class="terms-conditions mb-4">
                    <h5>الشروط والأحكام:</h5>
                    <p>${data.terms_conditions}</p>
                </div>
            ` : ''}
            
            <div class="signature-section mt-5">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="signature-box">
                            <p>توقيع الطرف الأول</p>
                            <div style="height: 80px; border: 1px solid #ccc; margin: 10px 0;"></div>
                            <p>التاريخ: ___________</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="signature-box">
                            <p>توقيع الطرف الثاني</p>
                            <div style="height: 80px; border: 1px solid #ccc; margin: 10px 0;"></div>
                            <p>التاريخ: ___________</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="signature-box">
                            <p>ختم الشركة</p>
                            <div style="height: 80px; border: 1px solid #ccc; margin: 10px 0;"></div>
                            <p>التاريخ: ___________</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return previewHtml;
}

// Auto-save functionality
function initAutoSave() {
    var saveTimer;
    
    // Save on content change
    quill.on('text-change', function() {
        clearTimeout(saveTimer);
        saveTimer = setTimeout(function() {
            autoSaveContract();
        }, 3000); // Save after 3 seconds of inactivity
    });
    
    // Save on form field change
    $('#contract-form input, #contract-form select, #contract-form textarea').on('input change', function() {
        clearTimeout(saveTimer);
        saveTimer = setTimeout(function() {
            autoSaveContract();
        }, 3000);
    });
}

// Auto-save contract data
function autoSaveContract() {
    // Only auto-save if we have basic required data
    if ($('#title').val() && $('#first_party_name').val() && $('#second_party_name').val()) {
        var formData = $('#contract-form').serialize();
        formData += '&content=' + encodeURIComponent(quill.root.innerHTML);
        formData += '&auto_save=true';
        
        // Save to localStorage as backup
        localStorage.setItem('contract_draft', JSON.stringify({
            title: $('#title').val(),
            content: quill.root.innerHTML,
            timestamp: new Date().toISOString()
        }));
        
        console.log('Auto-saved contract draft');
    }
}

// Load draft from localStorage
function loadDraft() {
    var draft = localStorage.getItem('contract_draft');
    if (draft) {
        try {
            var draftData = JSON.parse(draft);
            if (confirm('تم العثور على مسودة محفوظة. هل تريد استعادتها؟')) {
                $('#title').val(draftData.title);
                quill.root.innerHTML = draftData.content;
                localStorage.removeItem('contract_draft');
            }
        } catch (e) {
            console.error('Error loading draft:', e);
        }
    }
}

// Clear draft
function clearDraft() {
    localStorage.removeItem('contract_draft');
}

// Load draft on page load
$(document).ready(function() {
    setTimeout(loadDraft, 1000);
});
