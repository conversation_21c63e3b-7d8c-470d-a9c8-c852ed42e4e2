from flask import Flask, render_template, redirect, url_for
from flask_login import login_required
import os
from config import config
from models import init_app, db
from models.user import User
from models.contract_type import ContractType
from routes import register_blueprints

def create_app(config_name='default'):
    """Create Flask application"""
    app = Flask(__name__)
    
    # Load configuration
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    init_app(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Create upload directories
    create_directories(app)
    
    # Initialize database
    with app.app_context():
        db.create_all()
        create_default_data()
    
    # Root route
    @app.route('/')
    def index():
        return redirect(url_for('auth.login'))
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    return app

def create_directories(app):
    """Create necessary directories"""
    directories = [
        app.config['UPLOAD_FOLDER'],
        'static/uploads/contracts',
        'static/uploads/signatures',
        'static/exports/pdf',
        'static/exports/word'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def create_default_data():
    """Create default users and contract types"""
    # Create default admin user if not exists
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            full_name='مدير النظام',
            role='manager'
        )
        db.session.add(admin)
    
    # Create default employee user if not exists
    employee = User.query.filter_by(username='employee').first()
    if not employee:
        employee = User(
            username='employee',
            email='<EMAIL>',
            password='employee123',
            full_name='موظف النظام',
            role='employee'
        )
        db.session.add(employee)
    
    # Create default contract types if not exist
    if ContractType.query.count() == 0:
        default_types = ContractType.get_default_types()
        for type_data in default_types:
            contract_type = ContractType(
                name=type_data['name'],
                description=type_data['description'],
                template=type_data['template'],
                fields=type_data['fields']
            )
            db.session.add(contract_type)
    
    try:
        db.session.commit()
        print("Default data created successfully")
    except Exception as e:
        db.session.rollback()
        print(f"Error creating default data: {e}")

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
