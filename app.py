from flask import Flask, render_template, redirect, url_for
from flask_login import login_required
import os
import re
from config import config
from models import init_app, db
from models.user import User
from models.contract_type import ContractType
from routes import register_blueprints

def create_app(config_name='default'):
    """Create Flask application"""
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions
    init_app(app)

    # Register custom Jinja2 filters
    register_filters(app)

    # Register blueprints
    register_blueprints(app)

    # Create upload directories
    create_directories(app)

    # Initialize database
    with app.app_context():
        db.create_all()
        create_default_data()

    # Root route
    @app.route('/')
    def index():
        return redirect(url_for('auth.login'))

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    return app

def register_filters(app):
    """Register custom Jinja2 filters"""

    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to HTML line breaks"""
        if not text:
            return text
        # Replace \r\n, \r, and \n with <br>
        text = re.sub(r'\r\n|\r|\n', '<br>', str(text))
        return text

    @app.template_filter('currency')
    def currency_filter(amount, currency='QAR'):
        """Format currency with proper locale"""
        if not amount:
            return '0'

        try:
            amount = float(amount)
            if currency == 'QAR':
                return f"{amount:,.2f} ر.ق"
            elif currency == 'SAR':
                return f"{amount:,.2f} ر.س"
            elif currency == 'AED':
                return f"{amount:,.2f} د.إ"
            elif currency == 'KWD':
                return f"{amount:,.3f} د.ك"
            elif currency == 'BHD':
                return f"{amount:,.3f} د.ب"
            elif currency == 'USD':
                return f"${amount:,.2f}"
            elif currency == 'EUR':
                return f"€{amount:,.2f}"
            else:
                return f"{amount:,.2f} {currency}"
        except (ValueError, TypeError):
            return str(amount)

    @app.template_filter('hijri_date')
    def hijri_date_filter(gregorian_date):
        """Convert Gregorian date to approximate Hijri date"""
        if not gregorian_date:
            return ''

        try:
            from datetime import datetime
            if isinstance(gregorian_date, str):
                date = datetime.strptime(gregorian_date, '%Y-%m-%d')
            else:
                date = gregorian_date

            # Simple approximation - for accurate conversion, use a proper Hijri calendar library
            hijri_year = int((date.year - 622) * 1.030684) + 1400
            hijri_month = int((date.month) * 1.030684) % 12 + 1
            hijri_day = int(date.day * 1.030684) % 30 + 1

            hijri_months = [
                '', 'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى',
                'جمادى الثانية', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ]

            return f"{hijri_day} {hijri_months[hijri_month]} {hijri_year}هـ"
        except:
            return str(gregorian_date)

def create_directories(app):
    """Create necessary directories"""
    directories = [
        app.config['UPLOAD_FOLDER'],
        'static/uploads/contracts',
        'static/uploads/signatures',
        'static/exports/pdf',
        'static/exports/word'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def create_default_data():
    """Create default users and contract types"""
    # Create default admin user if not exists
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            full_name='مدير النظام',
            role='manager'
        )
        db.session.add(admin)
    
    # Create default employee user if not exists
    employee = User.query.filter_by(username='employee').first()
    if not employee:
        employee = User(
            username='employee',
            email='<EMAIL>',
            password='employee123',
            full_name='موظف النظام',
            role='employee'
        )
        db.session.add(employee)
    
    # Create default contract types if not exist
    if ContractType.query.count() == 0:
        default_types = ContractType.get_default_types()
        for type_data in default_types:
            contract_type = ContractType(
                name=type_data['name'],
                description=type_data['description'],
                template=type_data['template'],
                fields=type_data['fields']
            )
            db.session.add(contract_type)
    
    try:
        db.session.commit()
        print("Default data created successfully")
    except Exception as e:
        db.session.rollback()
        print(f"Error creating default data: {e}")

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
