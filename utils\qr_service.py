import qrcode
import os
import base64
from io import BytesIO
from datetime import datetime
import hashlib
import json

class QRService:
    def __init__(self):
        self.qr_dir = os.path.join('static', 'qr_codes')
        os.makedirs(self.qr_dir, exist_ok=True)
    
    def generate_contract_qr(self, contract, include_verification=True):
        """Generate QR code for contract"""
        
        try:
            # Create QR data
            qr_data = self._create_qr_data(contract, include_verification)
            
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            
            qr.add_data(qr_data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_image = qr.make_image(fill_color="black", back_color="white")
            
            # Save to file
            filename = f"contract_{contract.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            file_path = os.path.join(self.qr_dir, filename)
            qr_image.save(file_path)
            
            # Convert to base64 for embedding
            buffer = BytesIO()
            qr_image.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                'success': True,
                'file_path': file_path,
                'base64': qr_base64,
                'data': qr_data,
                'verification_hash': self._generate_verification_hash(contract) if include_verification else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في إنشاء QR Code: {str(e)}'
            }
    
    def _create_qr_data(self, contract, include_verification=True):
        """Create QR code data"""
        
        # Base URL for contract verification
        base_url = "https://contracts.gov.qa/verify"  # Replace with actual domain
        
        qr_data = {
            'contract_number': contract.contract_number,
            'title': contract.title,
            'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else None,
            'first_party': contract.first_party_name,
            'second_party': contract.second_party_name,
            'status': contract.status,
            'created_at': contract.created_at.strftime('%Y-%m-%d %H:%M:%S') if contract.created_at else None,
            'verify_url': f"{base_url}/{contract.contract_number}"
        }
        
        if contract.contract_value:
            qr_data['value'] = str(contract.contract_value)
            qr_data['currency'] = contract.currency
        
        if include_verification:
            qr_data['verification_hash'] = self._generate_verification_hash(contract)
            qr_data['qr_generated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Convert to JSON string
        return json.dumps(qr_data, ensure_ascii=False)
    
    def _generate_verification_hash(self, contract):
        """Generate verification hash for contract"""
        
        # Create hash from contract data
        hash_data = f"{contract.contract_number}_{contract.title}_{contract.first_party_name}_{contract.second_party_name}"
        
        if contract.contract_date:
            hash_data += f"_{contract.contract_date.strftime('%Y-%m-%d')}"
        
        if contract.contract_value:
            hash_data += f"_{contract.contract_value}"
        
        # Add creation timestamp for uniqueness
        hash_data += f"_{contract.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Generate SHA-256 hash
        return hashlib.sha256(hash_data.encode('utf-8')).hexdigest()[:16]
    
    def verify_contract_qr(self, qr_data_string):
        """Verify contract QR code"""
        
        try:
            qr_data = json.loads(qr_data_string)
            
            # Extract contract number
            contract_number = qr_data.get('contract_number')
            if not contract_number:
                return {
                    'success': False,
                    'message': 'رقم العقد غير موجود في QR Code'
                }
            
            # Find contract in database
            from models.contract import Contract
            contract = Contract.query.filter_by(contract_number=contract_number).first()
            
            if not contract:
                return {
                    'success': False,
                    'message': 'العقد غير موجود في النظام'
                }
            
            # Verify hash if present
            if 'verification_hash' in qr_data:
                expected_hash = self._generate_verification_hash(contract)
                if qr_data['verification_hash'] != expected_hash:
                    return {
                        'success': False,
                        'message': 'فشل في التحقق من صحة العقد - البيانات قد تكون محرفة'
                    }
            
            # Verify basic data
            verification_results = {
                'contract_found': True,
                'title_match': qr_data.get('title') == contract.title,
                'first_party_match': qr_data.get('first_party') == contract.first_party_name,
                'second_party_match': qr_data.get('second_party') == contract.second_party_name,
                'status_match': qr_data.get('status') == contract.status
            }
            
            if contract.contract_date:
                verification_results['date_match'] = qr_data.get('date') == contract.contract_date.strftime('%Y-%m-%d')
            
            if contract.contract_value:
                verification_results['value_match'] = qr_data.get('value') == str(contract.contract_value)
            
            # Check if all verifications passed
            all_verified = all(verification_results.values())
            
            return {
                'success': True,
                'verified': all_verified,
                'contract': {
                    'id': contract.id,
                    'contract_number': contract.contract_number,
                    'title': contract.title,
                    'status': contract.status,
                    'first_party_name': contract.first_party_name,
                    'second_party_name': contract.second_party_name,
                    'contract_date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else None,
                    'contract_value': contract.contract_value,
                    'currency': contract.currency,
                    'is_signed': contract.is_signed()
                },
                'verification_results': verification_results,
                'qr_data': qr_data
            }
            
        except json.JSONDecodeError:
            return {
                'success': False,
                'message': 'تنسيق QR Code غير صحيح'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في التحقق من QR Code: {str(e)}'
            }
    
    def generate_verification_qr(self, contract_number):
        """Generate simple verification QR code"""
        
        try:
            verify_url = f"https://contracts.gov.qa/verify/{contract_number}"
            
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=2,
            )
            
            qr.add_data(verify_url)
            qr.make(fit=True)
            
            qr_image = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = BytesIO()
            qr_image.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                'success': True,
                'base64': qr_base64,
                'url': verify_url
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'فشل في إنشاء QR Code للتحقق: {str(e)}'
            }
    
    def create_qr_html(self, contract, qr_base64, verification_hash=None):
        """Create HTML for QR code section"""
        
        html = f"""
        <div class="qr-section" style="margin-top: 50px; padding: 30px; border: 2px solid #e9ecef; border-radius: 10px; text-align: center; background-color: #f8f9fa;">
            <h4 style="color: #333; margin-bottom: 20px;">رمز التحقق الإلكتروني</h4>
            
            <div style="display: inline-block; padding: 20px; background-color: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <img src="data:image/png;base64,{qr_base64}" alt="QR Code للتحقق من العقد" style="max-width: 150px; height: auto;">
            </div>
            
            <div style="margin-top: 20px; font-size: 14px; color: #666;">
                <p><strong>رقم العقد:</strong> {contract.contract_number}</p>
                <p><strong>تاريخ الإنشاء:</strong> {contract.created_at.strftime('%Y-%m-%d %H:%M') if contract.created_at else ''}</p>
                {f'<p><strong>رمز التحقق:</strong> {verification_hash}</p>' if verification_hash else ''}
                <p style="margin-top: 15px; font-size: 12px; color: #999;">
                    امسح الرمز للتحقق من صحة العقد إلكترونياً
                </p>
            </div>
        </div>
        """
        
        return html
