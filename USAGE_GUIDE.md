# 📖 دليل الاستخدام السريع - المميزات الجديدة

## 🚀 المميزات الجديدة المضافة

### 1. 📧 إرسال العقود عبر البريد الإلكتروني

#### كيفية الاستخدام:
1. افتح أي عقد من قائمة العقود
2. انقر على زر **"إرسال"** → **"بريد إلكتروني"**
3. املأ البيانات:
   - البريد الإلكتروني للمستلم
   - اسم المستلم
   - رسالة مخصصة (اختيارية)
   - اختر المرفقات (PDF/Word)
4. انقر **"إرسال"**

#### الإعدادات المطلوبة:
- اذهب إلى **"إعدادات النظام"** (للمدير فقط)
- تبويب **"البريد الإلكتروني"**
- أدخل إعدادات SMTP الخاصة بك

### 2. 📱 إرسال العقود عبر WhatsApp

#### كيفية الاستخدام:
1. افتح أي عقد من قائمة العقود
2. انقر على زر **"إرسال"** → **"WhatsApp"**
3. املأ البيانات:
   - رقم الهاتف (مع أو بدون رمز الدولة)
   - اسم المستلم
   - رسالة مخصصة (اختيارية)
   - اختر إرفاق PDF
4. انقر **"إرسال"**

#### الإعدادات المطلوبة:
- تحتاج إلى حساب WhatsApp Business API
- اذهب إلى **"إعدادات النظام"** → **"WhatsApp"**
- أدخل بيانات API الخاصة بك

### 3. 🔒 QR Code للعقود

#### إنشاء QR Code:
1. افتح أي عقد
2. انقر على زر **"QR Code"**
3. سيتم إنشاء الرمز تلقائياً
4. يمكنك تحميل الصورة

#### التحقق من العقود:
1. اذهب إلى: `http://localhost:5000/verify`
2. امسح QR Code بالكاميرا أو ارفع صورة
3. أو أدخل رقم العقد يدوياً
4. اعرض تفاصيل التحقق

### 4. 🌐 العقود ثنائية اللغة

#### إنشاء عقد ثنائي اللغة:
1. افتح أي عقد
2. انقر على **"تصدير"** → **"ثنائي اللغة"**
3. سيفتح العقد في نافذة جديدة بالعربية والإنجليزية
4. يمكنك طباعة أو حفظ العقد

### 5. 💾 النسخ الاحتياطي التلقائي

#### إعدادات النسخ الاحتياطي:
1. اذهب إلى **"إعدادات النظام"** → **"النسخ الاحتياطي"**
2. فعل النسخ الاحتياطي التلقائي
3. حدد عدد النسخ المحفوظة
4. انقر **"إنشاء نسخة احتياطية الآن"** للاختبار

#### النسخ التلقائي:
- يتم إنشاء نسخة احتياطية يومياً في الساعة 2:00 صباحاً
- يتم حفظ 30 نسخة افتراضياً
- يتم حذف النسخ القديمة تلقائياً

### 6. 🔔 نظام الإشعارات المتقدم

#### عرض الإشعارات:
1. انقر على أيقونة الجرس في شريط التنقل
2. اعرض الإشعارات الحديثة
3. انقر على **"عرض جميع الإشعارات"** للصفحة الكاملة

#### أنواع الإشعارات:
- إنشاء عقد جديد
- تحديث العقد
- توقيع العقد
- انتهاء صلاحية العقد

## 🔧 استكشاف الأخطاء وإصلاحها

### مشكلة QR Code:
```bash
# إذا لم يعمل QR Code، قم بتثبيت المكتبة:
pip install qrcode[pil]
```

### مشكلة البريد الإلكتروني:
1. تأكد من إعدادات SMTP الصحيحة
2. استخدم App Password لـ Gmail
3. تأكد من تفعيل "Less secure app access"

### مشكلة WhatsApp:
1. تحتاج إلى حساب WhatsApp Business API مدفوع
2. احصل على API Token من Facebook Developers
3. تأكد من صحة Phone Number ID

### مشكلة النسخ الاحتياطي:
1. تأكد من وجود مساحة كافية على القرص
2. تأكد من صلاحيات الكتابة في مجلد backups
3. تحقق من سجلات النظام للأخطاء

## 📊 إحصائيات النظام

### المميزات المكتملة:
- ✅ **20+ نوع عقد** متخصص لقطر
- ✅ **إرسال البريد الإلكتروني** مع قوالب احترافية
- ✅ **إرسال WhatsApp** مع دعم المرفقات
- ✅ **QR Code** مع التحقق الإلكتروني
- ✅ **العقود ثنائية اللغة** (عربي/إنجليزي)
- ✅ **النسخ الاحتياطي التلقائي** مع الجدولة
- ✅ **نظام الإشعارات** المتقدم
- ✅ **البحث المتقدم** مع فلاتر شاملة
- ✅ **التوقيع الإلكتروني** المتقدم
- ✅ **دعم كامل للعربية** مع RTL

### الأرقام:
- **7 عملات** مدعومة
- **4 أنواع إشعارات**
- **9 فلاتر بحث**
- **5 خدمات متقدمة**
- **8 مسارات API** جديدة

## 🎯 الخطوات التالية

### للاستخدام الإنتاجي:
1. **كون إعدادات البريد الإلكتروني** في لوحة الإدارة
2. **احصل على WhatsApp Business API** إذا كنت تريد هذه الميزة
3. **فعل النسخ الاحتياطي التلقائي**
4. **اختبر جميع المميزات** قبل الاستخدام الفعلي
5. **درب المستخدمين** على المميزات الجديدة

### للتطوير المستقبلي:
1. إضافة المزيد من أنواع العقود
2. تحسين قوالب البريد الإلكتروني
3. إضافة إشعارات SMS
4. تطوير تطبيق موبايل
5. إضافة تقارير متقدمة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع ملف README.md للتفاصيل التقنية
3. تحقق من سجلات النظام (console logs)
4. تأكد من تثبيت جميع المتطلبات

---

**🎉 النظام جاهز للاستخدام مع جميع المميزات المتقدمة!**
