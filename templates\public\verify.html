<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من صحة العقد - نظام إدارة العقود الإلكترونية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .verify-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .verify-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .verify-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .verify-body {
            padding: 40px;
        }
        
        .qr-scanner {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .qr-scanner:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .qr-scanner.active {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .verification-result {
            display: none;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .verification-success {
            background-color: #d1edff;
            border: 1px solid #0ea5e9;
            color: #0369a1;
        }
        
        .verification-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        
        .contract-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #212529;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-verified {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-unverified {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <!-- Header -->
            <div class="verify-header">
                <h1 class="h3 mb-2">
                    <i class="fas fa-shield-alt me-2"></i>
                    التحقق من صحة العقد
                </h1>
                <p class="mb-0">نظام إدارة العقود الإلكترونية - دولة قطر</p>
            </div>
            
            <!-- Body -->
            <div class="verify-body">
                <!-- QR Scanner Section -->
                <div class="qr-scanner" id="qrScanner">
                    <i class="fas fa-qrcode fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted mb-3">امسح QR Code للتحقق من العقد</h5>
                    <p class="text-muted mb-4">
                        يمكنك مسح الرمز المربع الموجود في نهاية العقد للتحقق من صحته
                    </p>
                    
                    <!-- File Upload Option -->
                    <div class="mb-3">
                        <input type="file" class="form-control" id="qrImageUpload" accept="image/*" style="display: none;">
                        <button type="button" class="btn btn-outline-primary me-2" onclick="document.getElementById('qrImageUpload').click()">
                            <i class="fas fa-upload me-1"></i>
                            رفع صورة QR Code
                        </button>
                        <button type="button" class="btn btn-primary" onclick="startCamera()">
                            <i class="fas fa-camera me-1"></i>
                            تشغيل الكاميرا
                        </button>
                    </div>
                    
                    <!-- Camera Preview -->
                    <div id="cameraPreview" style="display: none;">
                        <video id="video" width="100%" height="200" autoplay></video>
                        <canvas id="canvas" style="display: none;"></canvas>
                        <div class="mt-2">
                            <button type="button" class="btn btn-success me-2" onclick="captureQR()">
                                <i class="fas fa-camera me-1"></i>
                                التقاط
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="stopCamera()">
                                <i class="fas fa-stop me-1"></i>
                                إيقاف
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Input Option -->
                <div class="text-center mb-4">
                    <p class="text-muted">أو</p>
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleManualInput()">
                        <i class="fas fa-keyboard me-1"></i>
                        إدخال رقم العقد يدوياً
                    </button>
                </div>
                
                <!-- Manual Input Form -->
                <div id="manualInputForm" style="display: none;">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">البحث برقم العقد</h6>
                            <div class="input-group">
                                <input type="text" class="form-control" id="contractNumber" 
                                       placeholder="أدخل رقم العقد...">
                                <button class="btn btn-primary" type="button" onclick="verifyByNumber()">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Verification Result -->
                <div id="verificationResult" class="verification-result">
                    <!-- Results will be displayed here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- QR Code Scanner Library -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    
    <script>
        let video = null;
        let canvas = null;
        let context = null;
        let scanning = false;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            canvas = document.getElementById('canvas');
            context = canvas.getContext('2d');
            video = document.getElementById('video');
            
            // Handle file upload
            document.getElementById('qrImageUpload').addEventListener('change', handleImageUpload);
        });
        
        // Start camera
        function startCamera() {
            navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
                .then(function(stream) {
                    video.srcObject = stream;
                    document.getElementById('cameraPreview').style.display = 'block';
                    scanning = true;
                    scanQRCode();
                })
                .catch(function(err) {
                    showAlert('فشل في تشغيل الكاميرا: ' + err.message, 'error');
                });
        }
        
        // Stop camera
        function stopCamera() {
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            document.getElementById('cameraPreview').style.display = 'none';
            scanning = false;
        }
        
        // Capture QR from camera
        function captureQR() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            const code = jsQR(imageData.data, imageData.width, imageData.height);
            
            if (code) {
                stopCamera();
                verifyQRData(code.data);
            } else {
                showAlert('لم يتم العثور على QR Code في الصورة', 'warning');
            }
        }
        
        // Scan QR code continuously
        function scanQRCode() {
            if (scanning && video.readyState === video.HAVE_ENOUGH_DATA) {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height);
                
                if (code) {
                    stopCamera();
                    verifyQRData(code.data);
                    return;
                }
            }
            
            if (scanning) {
                requestAnimationFrame(scanQRCode);
            }
        }
        
        // Handle image upload
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    context.drawImage(img, 0, 0);
                    
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const code = jsQR(imageData.data, imageData.width, imageData.height);
                    
                    if (code) {
                        verifyQRData(code.data);
                    } else {
                        showAlert('لم يتم العثور على QR Code في الصورة المرفوعة', 'warning');
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        // Toggle manual input
        function toggleManualInput() {
            const form = document.getElementById('manualInputForm');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }
        
        // Verify by contract number
        function verifyByNumber() {
            const contractNumber = document.getElementById('contractNumber').value.trim();
            if (!contractNumber) {
                showAlert('يرجى إدخال رقم العقد', 'warning');
                return;
            }
            
            // Create simple verification URL
            const verifyUrl = `https://contracts.gov.qa/verify/${contractNumber}`;
            verifyQRData(verifyUrl);
        }
        
        // Verify QR data
        function verifyQRData(qrData) {
            showLoading();
            
            fetch('/api/qr/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ qr_data: qrData })
            })
            .then(response => response.json())
            .then(result => {
                hideLoading();
                displayVerificationResult(result);
            })
            .catch(error => {
                hideLoading();
                showAlert('حدث خطأ أثناء التحقق من العقد', 'error');
            });
        }
        
        // Display verification result
        function displayVerificationResult(result) {
            const resultDiv = document.getElementById('verificationResult');
            
            if (result.success && result.verified) {
                const contract = result.contract;
                resultDiv.innerHTML = `
                    <div class="verification-success">
                        <div class="text-center mb-3">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                            <h5 class="mt-2 text-success">تم التحقق من صحة العقد بنجاح</h5>
                        </div>
                        
                        <div class="contract-details">
                            <h6 class="mb-3">تفاصيل العقد:</h6>
                            
                            <div class="detail-row">
                                <span class="detail-label">رقم العقد:</span>
                                <span class="detail-value">${contract.contract_number}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">عنوان العقد:</span>
                                <span class="detail-value">${contract.title}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">الطرف الأول:</span>
                                <span class="detail-value">${contract.first_party_name}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">الطرف الثاني:</span>
                                <span class="detail-value">${contract.second_party_name}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">تاريخ العقد:</span>
                                <span class="detail-value">${contract.contract_date || 'غير محدد'}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">حالة العقد:</span>
                                <span class="detail-value">
                                    <span class="status-badge ${contract.status === 'active' ? 'status-verified' : 'status-unverified'}">
                                        ${getStatusText(contract.status)}
                                    </span>
                                </span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">حالة التوقيع:</span>
                                <span class="detail-value">
                                    <span class="status-badge ${contract.is_signed ? 'status-verified' : 'status-unverified'}">
                                        ${contract.is_signed ? 'موقع' : 'غير موقع'}
                                    </span>
                                </span>
                            </div>
                            
                            ${contract.contract_value ? `
                            <div class="detail-row">
                                <span class="detail-label">قيمة العقد:</span>
                                <span class="detail-value">${contract.contract_value} ${contract.currency}</span>
                            </div>
                            ` : ''}
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                تم التحقق من صحة هذا العقد بواسطة نظام إدارة العقود الإلكترونية - دولة قطر
                            </small>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="verification-error">
                        <div class="text-center">
                            <i class="fas fa-times-circle fa-3x text-danger"></i>
                            <h5 class="mt-2 text-danger">فشل في التحقق من العقد</h5>
                            <p class="mb-0">${result.message || 'العقد غير صحيح أو غير موجود في النظام'}</p>
                        </div>
                    </div>
                `;
            }
            
            resultDiv.style.display = 'block';
        }
        
        // Helper functions
        function getStatusText(status) {
            const statusMap = {
                'draft': 'مسودة',
                'active': 'نشط',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };
            return statusMap[status] || status;
        }
        
        function showLoading() {
            document.getElementById('verificationResult').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحقق...</span>
                    </div>
                    <p class="mt-2">جاري التحقق من صحة العقد...</p>
                </div>
            `;
            document.getElementById('verificationResult').style.display = 'block';
        }
        
        function hideLoading() {
            // Loading will be replaced by result
        }
        
        function showAlert(message, type) {
            // Simple alert for now - could be enhanced with toast notifications
            alert(message);
        }
    </script>
</body>
</html>
