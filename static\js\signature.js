// Electronic Signature JavaScript

let signaturePad;

$(document).ready(function() {
    // Initialize signature pad when modal is shown
    $('#signatureModal').on('shown.bs.modal', function() {
        initializeSignaturePad();
    });
});

// Initialize signature pad
function initializeSignaturePad() {
    const canvas = document.getElementById('signature-pad');
    
    // Resize canvas to fit container
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext('2d').scale(ratio, ratio);
        signaturePad.clear();
    }
    
    signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)',
        velocityFilterWeight: 0.7,
        minWidth: 0.5,
        maxWidth: 2.5,
        throttle: 16,
        minDistance: 5
    });
    
    // Resize canvas on window resize
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
}

// Open signature modal
function openSignatureModal(signatureType) {
    currentSignatureType = signatureType;
    
    // Set modal title based on signature type
    let title = '';
    switch(signatureType) {
        case 'first_party':
            title = 'توقيع الطرف الأول';
            break;
        case 'second_party':
            title = 'توقيع الطرف الثاني';
            break;
        case 'company_seal':
            title = 'ختم الشركة';
            break;
    }
    
    $('#signatureModalTitle').text(title);
    $('#signatureModal').modal('show');
}

// Clear signature
function clearSignature() {
    if (signaturePad) {
        signaturePad.clear();
    }
}

// Save signature
function saveSignature() {
    if (!signaturePad) {
        showAlert('خطأ في تهيئة لوحة التوقيع', 'error');
        return;
    }
    
    if (signaturePad.isEmpty()) {
        showAlert('يرجى إضافة التوقيع أولاً', 'warning');
        return;
    }
    
    // Get signature data as base64
    const signatureData = signaturePad.toDataURL('image/png');
    
    // Send to server
    $.ajax({
        url: `/api/contracts/${contractId}/signature`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            type: currentSignatureType,
            signature: signatureData
        }),
        success: function(response) {
            if (response.success) {
                showAlert('تم حفظ التوقيع بنجاح', 'success');
                $('#signatureModal').modal('hide');
                
                // Update signature display
                updateSignatureDisplay(currentSignatureType, signatureData);
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            showAlert('حدث خطأ أثناء حفظ التوقيع', 'error');
            console.error('Signature save error:', error);
        }
    });
}

// Update signature display in the contract view
function updateSignatureDisplay(signatureType, signatureData) {
    const signatureBoxes = document.querySelectorAll('.signature-box');
    let targetBox;
    
    switch(signatureType) {
        case 'first_party':
            targetBox = signatureBoxes[0];
            break;
        case 'second_party':
            targetBox = signatureBoxes[1];
            break;
        case 'company_seal':
            targetBox = signatureBoxes[2];
            break;
    }
    
    if (targetBox) {
        const placeholder = targetBox.querySelector('.signature-placeholder');
        if (placeholder) {
            // Replace placeholder with signature image
            placeholder.innerHTML = `<img src="${signatureData}" class="img-fluid" style="max-height: 100px;">`;
        }
    }
}

// Upload signature from file
function uploadSignatureFile(signatureType) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        // Validate file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            showAlert('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت', 'error');
            return;
        }
        
        // Validate file type
        if (!file.type.startsWith('image/')) {
            showAlert('يرجى اختيار ملف صورة صحيح', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const signatureData = e.target.result;
            
            // Send to server
            $.ajax({
                url: `/api/contracts/${contractId}/signature`,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    type: signatureType,
                    signature: signatureData
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('تم رفع التوقيع بنجاح', 'success');
                        updateSignatureDisplay(signatureType, signatureData);
                    } else {
                        showAlert(response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء رفع التوقيع', 'error');
                }
            });
        };
        
        reader.readAsDataURL(file);
    };
    
    input.click();
}

// Remove signature
function removeSignature(signatureType) {
    if (!confirm('هل أنت متأكد من حذف التوقيع؟')) {
        return;
    }
    
    $.ajax({
        url: `/api/contracts/${contractId}/signature`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            type: signatureType,
            signature: ''
        }),
        success: function(response) {
            if (response.success) {
                showAlert('تم حذف التوقيع', 'success');
                
                // Update display to show placeholder
                const signatureBoxes = document.querySelectorAll('.signature-box');
                let targetBox;
                
                switch(signatureType) {
                    case 'first_party':
                        targetBox = signatureBoxes[0];
                        break;
                    case 'second_party':
                        targetBox = signatureBoxes[1];
                        break;
                    case 'company_seal':
                        targetBox = signatureBoxes[2];
                        break;
                }
                
                if (targetBox) {
                    const placeholder = targetBox.querySelector('.signature-placeholder');
                    if (placeholder) {
                        placeholder.innerHTML = '<span class="text-muted">غير موقع</span>';
                    }
                }
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء حذف التوقيع', 'error');
        }
    });
}

// Signature pad touch support for mobile devices
function addTouchSupport() {
    const canvas = document.getElementById('signature-pad');
    
    // Prevent scrolling when touching the canvas
    canvas.addEventListener('touchstart', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    canvas.addEventListener('touchend', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    canvas.addEventListener('touchmove', function(e) {
        e.preventDefault();
    }, { passive: false });
}

// Initialize touch support when modal is shown
$('#signatureModal').on('shown.bs.modal', function() {
    setTimeout(addTouchSupport, 100);
});

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Only work when signature modal is open
    if (!$('#signatureModal').hasClass('show')) return;
    
    // Escape key to close modal
    if (e.key === 'Escape') {
        $('#signatureModal').modal('hide');
    }
    
    // Ctrl+Z to clear signature
    if (e.ctrlKey && e.key === 'z') {
        e.preventDefault();
        clearSignature();
    }
    
    // Enter to save signature
    if (e.key === 'Enter') {
        e.preventDefault();
        saveSignature();
    }
});

// Auto-resize signature pad on modal resize
$('#signatureModal').on('shown.bs.modal', function() {
    const modal = this;
    const resizeObserver = new ResizeObserver(function() {
        if (signaturePad) {
            const canvas = document.getElementById('signature-pad');
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext('2d').scale(ratio, ratio);
        }
    });
    
    resizeObserver.observe(modal);
    
    // Clean up observer when modal is hidden
    $('#signatureModal').on('hidden.bs.modal', function() {
        resizeObserver.disconnect();
    });
});
