#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for custom Jinja2 filters
"""

from app import create_app
from datetime import datetime

def test_filters():
    """Test all custom Jinja2 filters"""
    app = create_app()
    
    with app.app_context():
        # Test nl2br filter
        print("Testing nl2br filter:")
        test_text = "السطر الأول\nالسطر الثاني\r\nالسطر الثالث"
        with app.test_request_context():
            from flask import render_template_string
            result = render_template_string("{{ text|nl2br|safe }}", text=test_text)
            print(f"Input: {repr(test_text)}")
            print(f"Output: {result}")
            print()
        
        # Test currency filter
        print("Testing currency filter:")
        test_amounts = [1000, 1500.50, 2000000]
        currencies = ['QAR', 'SAR', 'USD', 'EUR', 'AED', 'KWD', 'BHD']
        
        for amount in test_amounts:
            for currency in currencies:
                with app.test_request_context():
                    result = render_template_string("{{ amount|currency(currency) }}", 
                                                  amount=amount, currency=currency)
                    print(f"{amount} {currency} -> {result}")
        print()
        
        # Test hijri_date filter
        print("Testing hijri_date filter:")
        test_dates = [
            datetime(2024, 1, 1),
            datetime(2024, 6, 15),
            datetime(2024, 12, 31),
            "2024-03-15"
        ]
        
        for date in test_dates:
            with app.test_request_context():
                result = render_template_string("{{ date|hijri_date }}", date=date)
                print(f"{date} -> {result}")
        print()
        
        print("All filters tested successfully!")

if __name__ == '__main__':
    test_filters()
