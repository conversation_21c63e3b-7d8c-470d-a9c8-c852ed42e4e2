#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for QR and Bilingual services
"""

def test_qr_simple():
    """Simple QR test"""
    print("🔍 Testing QR Service...")
    
    try:
        from utils.qr_service import QRService
        
        # Mock contract
        class MockContract:
            def __init__(self):
                self.id = 1
                self.contract_number = "TEST-001"
                self.title = "Test Contract"
                self.first_party_name = "Party 1"
                self.second_party_name = "Party 2"
                self.contract_date = None
                self.contract_value = 1000
                self.currency = "QAR"
                self.status = "active"
                self.created_at = None
        
        contract = MockContract()
        qr_service = QRService()
        result = qr_service.generate_contract_qr(contract, include_verification=True)
        
        print(f"   Result: {result.get('success', False)}")
        if result.get('success'):
            print(f"   Base64 length: {len(result.get('base64', ''))}")
        else:
            print(f"   Error: {result.get('message', 'Unknown')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_bilingual_simple():
    """Simple bilingual test"""
    print("\n🌐 Testing Bilingual Service...")
    
    try:
        from utils.bilingual_service import BilingualService
        
        # Mock contract
        class MockContractType:
            def __init__(self):
                self.name = "Test Type"
        
        class MockContract:
            def __init__(self):
                self.id = 1
                self.contract_number = "TEST-001"
                self.title = "Test Contract"
                self.first_party_name = "Party 1"
                self.second_party_name = "Party 2"
                self.contract_date = None
                self.contract_value = 1000
                self.currency = "QAR"
                self.content = "Test content"
                self.terms_conditions = "Test terms"
                self.contract_type = MockContractType()
        
        contract = MockContract()
        bilingual_service = BilingualService()
        result = bilingual_service.create_bilingual_contract(contract, include_english=True)
        
        print(f"   Result: {result.get('success', False)}")
        if result.get('success'):
            print(f"   Content length: {len(result.get('content', ''))}")
        else:
            print(f"   Error: {result.get('message', 'Unknown')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"   Error: {e}")
        return False

if __name__ == '__main__':
    print("🧪 Simple Service Test")
    print("=" * 30)
    
    qr_ok = test_qr_simple()
    bilingual_ok = test_bilingual_simple()
    
    print("\n" + "=" * 30)
    print(f"QR Service: {'✅' if qr_ok else '❌'}")
    print(f"Bilingual Service: {'✅' if bilingual_ok else '❌'}")
    
    if qr_ok and bilingual_ok:
        print("\n🎉 Both services work!")
    else:
        print("\n⚠️ Some services have issues.")
