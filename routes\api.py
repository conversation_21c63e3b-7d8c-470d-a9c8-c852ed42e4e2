from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
import json
import base64
from datetime import datetime
from models import db
from models.contract import Contract
from models.contract_type import ContractType
from models.notification import Notification
try:
    from utils.email_service import EmailService
except ImportError as e:
    print(f"Warning: EmailService not available: {e}")
    EmailService = None

try:
    from utils.whatsapp_service import WhatsAppService
except ImportError as e:
    print(f"Warning: WhatsAppService not available: {e}")
    WhatsAppService = None

try:
    from utils.qr_service import QRService
except ImportError as e:
    print(f"Warning: QRService not available: {e}")
    QRService = None

try:
    from utils.bilingual_service import BilingualService
except ImportError as e:
    print(f"Warning: BilingualService not available: {e}")
    BilingualService = None

try:
    from utils.backup_service import BackupService
except ImportError as e:
    print(f"Warning: BackupService not available: {e}")
    BackupService = None

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.errorhandler(404)
def api_not_found(error):
    return jsonify({'success': False, 'message': 'المسار غير موجود'}), 404

@api_bp.errorhandler(500)
def api_internal_error(error):
    return jsonify({'success': False, 'message': 'خطأ داخلي في الخادم'}), 500

@api_bp.errorhandler(401)
def api_unauthorized(error):
    return jsonify({'success': False, 'message': 'غير مصرح - يجب تسجيل الدخول'}), 401

@api_bp.errorhandler(403)
def api_forbidden(error):
    return jsonify({'success': False, 'message': 'ممنوع - ليس لديك صلاحية'}), 403

@api_bp.route('/contract_types')
@login_required
def get_contract_types():
    """Get all active contract types"""
    contract_types = ContractType.query.filter_by(is_active=True).all()
    return jsonify([ct.to_dict() for ct in contract_types])

@api_bp.route('/contract_type/<int:id>/template')
@login_required
def get_contract_template(id):
    """Get contract type template"""
    contract_type = ContractType.query.get_or_404(id)
    return jsonify({
        'template': contract_type.template,
        'fields': contract_type.fields
    })

@api_bp.route('/contracts/<int:id>/signature', methods=['POST'])
@login_required
def save_signature(id):
    """Save electronic signature for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتوقيع هذا العقد'})
    
    data = request.get_json()
    signature_type = data.get('type')  # first_party, second_party, company_seal
    signature_data = data.get('signature')  # Base64 encoded image
    
    if not signature_type or not signature_data:
        return jsonify({'success': False, 'message': 'بيانات التوقيع غير مكتملة'})
    
    try:
        # Validate signature type
        if signature_type == 'first_party':
            contract.first_party_signature = signature_data
        elif signature_type == 'second_party':
            contract.second_party_signature = signature_data
        elif signature_type == 'company_seal':
            contract.company_seal = signature_data
        else:
            return jsonify({'success': False, 'message': 'نوع التوقيع غير صحيح'})
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حفظ التوقيع بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ التوقيع'})

@api_bp.route('/contracts/<int:id>/attachments', methods=['POST'])
@login_required
def upload_attachment(id):
    """Upload file attachment for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتعديل هذا العقد'})
    
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    # Validate file type
    allowed_extensions = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
    if not ('.' in file.filename and 
            file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'})
    
    try:
        # Save file
        import os
        from werkzeug.utils import secure_filename
        
        filename = secure_filename(file.filename)
        upload_folder = os.path.join('static', 'uploads', 'contracts', str(contract.id))
        os.makedirs(upload_folder, exist_ok=True)
        
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)
        
        # Update contract attachments
        attachments = contract.attachments or []
        attachments.append({
            'filename': filename,
            'path': file_path,
            'uploaded_by': current_user.id,
            'uploaded_at': datetime.utcnow().isoformat()
        })
        contract.attachments = attachments
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم رفع الملف بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء رفع الملف'})

@api_bp.route('/contracts/search')
@login_required
def search_contracts():
    """Search contracts via API"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    # Build search query
    contracts_query = Contract.query
    
    # Filter by user role
    if not current_user.is_manager():
        contracts_query = contracts_query.filter_by(created_by=current_user.id)
    
    if query:
        from sqlalchemy import or_
        contracts_query = contracts_query.filter(
            or_(
                Contract.title.contains(query),
                Contract.first_party_name.contains(query),
                Contract.second_party_name.contains(query),
                Contract.contract_number.contains(query)
            )
        )
    
    contracts = contracts_query.limit(limit).all()
    return jsonify([contract.to_dict() for contract in contracts])

@api_bp.route('/contracts/<int:id>/preview')
@login_required
def preview_contract(id):
    """Get contract preview data"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not current_user.is_manager() and contract.created_by != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})
    
    # Process content with variables
    content = contract.content
    
    # Replace variables in content
    variables = {
        'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '',
        'first_party_name': contract.first_party_name,
        'second_party_name': contract.second_party_name,
        'contract_number': contract.contract_number,
        'contract_value': str(contract.contract_value) if contract.contract_value else '',
        'currency': contract.currency or 'QAR'
    }
    
    # Add custom fields
    if contract.custom_fields:
        variables.update(contract.custom_fields)
    
    # Replace variables in content
    for key, value in variables.items():
        content = content.replace(f'{{{{{key}}}}}', str(value))
    
    return jsonify({
        'success': True,
        'content': content,
        'contract': contract.to_dict()
    })

@api_bp.route('/notifications')
@login_required
def get_notifications():
    """Get user notifications"""
    notifications = Notification.get_recent_notifications(current_user.id, limit=20)
    unread_count = Notification.get_unread_count(current_user.id)

    return jsonify({
        'success': True,
        'notifications': [notification.to_dict() for notification in notifications],
        'unread_count': unread_count
    })

@api_bp.route('/notifications', methods=['POST'])
@login_required
def create_notification():
    """Create new notification"""
    data = request.get_json()

    title = data.get('title')
    message = data.get('message')
    notification_type = data.get('type', 'info')
    contract_id = data.get('contract_id')

    if not title or not message:
        return jsonify({'success': False, 'message': 'العنوان والرسالة مطلوبان'})

    try:
        notification = Notification(
            user_id=current_user.id,
            title=title,
            message=message,
            type=notification_type,
            contract_id=contract_id
        )

        db.session.add(notification)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء الإشعار بنجاح',
            'notification': notification.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء إنشاء الإشعار'})

@api_bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark notification as read"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first()

    if not notification:
        return jsonify({'success': False, 'message': 'الإشعار غير موجود'})

    try:
        notification.mark_as_read()
        return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
    except Exception as e:
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث الإشعار'})

@api_bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    try:
        notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).all()

        for notification in notifications:
            notification.is_read = True

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم تحديد {len(notifications)} إشعار كمقروء'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث الإشعارات'})

@api_bp.route('/contracts/<int:contract_id>/send-email', methods=['POST'])
@login_required
def send_contract_email(contract_id):
    """Send contract via email"""
    if not EmailService:
        return jsonify({'success': False, 'message': 'خدمة البريد الإلكتروني غير متوفرة'})

    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json()
    recipient_email = data.get('email')
    recipient_name = data.get('name', 'العميل الكريم')
    include_pdf = data.get('include_pdf', True)
    include_word = data.get('include_word', False)
    custom_message = data.get('message')

    if not recipient_email:
        return jsonify({'success': False, 'message': 'البريد الإلكتروني مطلوب'})

    try:
        email_service = EmailService()
        result = email_service.send_contract_email(
            contract=contract,
            recipient_email=recipient_email,
            recipient_name=recipient_name,
            sent_by_user_id=current_user.id,
            include_pdf=include_pdf,
            include_word=include_word,
            custom_message=custom_message
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/contracts/<int:contract_id>/send-whatsapp', methods=['POST'])
@login_required
def send_contract_whatsapp(contract_id):
    """Send contract via WhatsApp"""
    if not WhatsAppService:
        return jsonify({'success': False, 'message': 'خدمة WhatsApp غير متوفرة'})

    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json()
    recipient_phone = data.get('phone')
    recipient_name = data.get('name', 'العميل الكريم')
    include_pdf = data.get('include_pdf', True)
    custom_message = data.get('message')

    if not recipient_phone:
        return jsonify({'success': False, 'message': 'رقم الهاتف مطلوب'})

    try:
        whatsapp_service = WhatsAppService()
        result = whatsapp_service.send_contract_whatsapp(
            contract=contract,
            recipient_phone=recipient_phone,
            recipient_name=recipient_name,
            sent_by_user_id=current_user.id,
            include_pdf=include_pdf,
            custom_message=custom_message
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/contracts/<int:contract_id>/generate-qr', methods=['POST'])
@login_required
def generate_contract_qr(contract_id):
    """Generate QR code for contract"""
    print(f"🔍 API: Generating QR for contract {contract_id}")

    try:
        # Check if user is authenticated
        if not current_user.is_authenticated:
            print("❌ User not authenticated")
            return jsonify({'success': False, 'message': 'يجب تسجيل الدخول أولاً'}), 401

        print(f"✅ User authenticated: {current_user.username}")

        contract = Contract.query.get_or_404(contract_id)
        print(f"✅ Contract found: {contract.contract_number}")

        if not contract.can_view(current_user):
            print("❌ User cannot view contract")
            return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'}), 403

        data = request.get_json() or {}
        include_verification = data.get('include_verification', True)
        print(f"📝 Include verification: {include_verification}")

        # Try to generate QR code directly
        try:
            print("📦 Generating QR code directly...")

            # Import QR libraries
            import qrcode
            from qrcode.constants import ERROR_CORRECT_L
            import base64
            from io import BytesIO
            import hashlib

            # Create QR data
            verify_url = f"http://localhost:5000/verify/{contract.contract_number}"

            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )

            qr.add_data(verify_url)
            qr.make(fit=True)

            # Create QR code image
            qr_image = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffer = BytesIO()
            qr_image.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()

            # Generate verification hash
            verification_hash = None
            if include_verification:
                hash_data = f"{contract.contract_number}_{contract.id}_{contract.created_at}"
                verification_hash = hashlib.md5(hash_data.encode()).hexdigest()[:8]

            print(f"✅ QR generated! Base64 length: {len(qr_base64)}")

            result = {
                'success': True,
                'base64': qr_base64,
                'data': verify_url,
                'verification_hash': verification_hash
            }

            return jsonify(result)

        except ImportError as e:
            print(f"❌ QR library not available: {e}")
            return jsonify({'success': False, 'message': f'مكتبة QR Code غير متوفرة. يرجى تثبيت qrcode[pil]'}), 500
        except Exception as e:
            print(f"❌ QR generation error: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'message': f'خطأ في إنشاء QR Code: {str(e)}'}), 500

    except Exception as e:
        print(f"❌ General API error: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'حدث خطأ عام: {str(e)}'}), 500

@api_bp.route('/contracts/<int:contract_id>/bilingual', methods=['POST'])
@login_required
def generate_bilingual_contract(contract_id):
    """Generate bilingual contract"""
    print(f"🌐 API: Generating bilingual contract for {contract_id}")

    try:
        # Check if user is authenticated
        if not current_user.is_authenticated:
            print("❌ User not authenticated")
            return jsonify({'success': False, 'message': 'يجب تسجيل الدخول أولاً'}), 401

        print(f"✅ User authenticated: {current_user.username}")

        contract = Contract.query.get_or_404(contract_id)
        print(f"✅ Contract found: {contract.contract_number}")

        if not contract.can_view(current_user):
            print("❌ User cannot view contract")
            return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'}), 403

        data = request.get_json() or {}
        include_english = data.get('include_english', True)
        print(f"📝 Include English: {include_english}")

        # Try to generate bilingual contract directly
        try:
            print("📦 Generating bilingual contract directly...")

            from datetime import datetime

            # Arabic content
            arabic_content = f"""
            <div class="contract-header" style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px;">
                    {contract.title or 'عقد'}
                </h1>
                <p style="color: #7f8c8d; font-size: 18px;">رقم العقد: {contract.contract_number}</p>
            </div>

            <div class="contract-parties" style="margin-bottom: 30px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div style="flex: 1; padding: 15px; background-color: #ecf0f1; border-radius: 8px; margin-left: 10px;">
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">الطرف الأول</h3>
                        <p style="font-size: 16px; margin: 0;">{contract.first_party_name}</p>
                    </div>
                    <div style="flex: 1; padding: 15px; background-color: #ecf0f1; border-radius: 8px; margin-right: 10px;">
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">الطرف الثاني</h3>
                        <p style="font-size: 16px; margin: 0;">{contract.second_party_name}</p>
                    </div>
                </div>
            </div>

            <div class="contract-details" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">تفاصيل العقد</h3>
                <p><strong>قيمة العقد:</strong> {contract.contract_value:,} {contract.currency}</p>
                <p><strong>تاريخ العقد:</strong> {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else 'غير محدد'}</p>
                <p><strong>نوع العقد:</strong> {contract.contract_type.name if contract.contract_type else 'غير محدد'}</p>
            </div>

            <div class="contract-content" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">محتوى العقد</h3>
                <div style="line-height: 1.8; text-align: justify;">
                    {contract.content or 'محتوى العقد غير متوفر'}
                </div>
            </div>

            <div class="contract-terms" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">الشروط والأحكام</h3>
                <div style="line-height: 1.8; text-align: justify;">
                    {contract.terms_conditions or 'الشروط والأحكام غير متوفرة'}
                </div>
            </div>
            """

            # English content (simplified translation)
            english_content = f"""
            <div class="contract-header" style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px;">
                    {contract.title or 'Contract'}
                </h1>
                <p style="color: #7f8c8d; font-size: 18px;">Contract Number: {contract.contract_number}</p>
            </div>

            <div class="contract-parties" style="margin-bottom: 30px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div style="flex: 1; padding: 15px; background-color: #ecf0f1; border-radius: 8px; margin-right: 10px;">
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">First Party</h3>
                        <p style="font-size: 16px; margin: 0;">{contract.first_party_name}</p>
                    </div>
                    <div style="flex: 1; padding: 15px; background-color: #ecf0f1; border-radius: 8px; margin-left: 10px;">
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">Second Party</h3>
                        <p style="font-size: 16px; margin: 0;">{contract.second_party_name}</p>
                    </div>
                </div>
            </div>

            <div class="contract-details" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Contract Details</h3>
                <p><strong>Contract Value:</strong> {contract.contract_value:,} {contract.currency}</p>
                <p><strong>Contract Date:</strong> {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else 'Not specified'}</p>
                <p><strong>Contract Type:</strong> {contract.contract_type.name if contract.contract_type else 'Not specified'}</p>
            </div>

            <div class="contract-content" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Contract Content</h3>
                <div style="line-height: 1.8; text-align: justify;">
                    {contract.content or 'Contract content not available'}
                </div>
            </div>

            <div class="contract-terms" style="margin-bottom: 30px;">
                <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Terms and Conditions</h3>
                <div style="line-height: 1.8; text-align: justify;">
                    {contract.terms_conditions or 'Terms and conditions not available'}
                </div>
            </div>
            """

            # Combined bilingual content
            bilingual_content = f"""
            <div class="bilingual-contract" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <!-- Arabic Version -->
                <div class="arabic-version" style="direction: rtl; text-align: right; margin-bottom: 50px;">
                    <h2 style="text-align: center; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
                        النسخة العربية
                    </h2>
                    {arabic_content}
                </div>

                <!-- English Version -->
                <div class="english-version" style="direction: ltr; text-align: left; margin-top: 50px; border-top: 2px solid #667eea; padding-top: 30px;">
                    <h2 style="text-align: center; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
                        English Version
                    </h2>
                    {english_content}
                </div>

                <!-- Legal Notice -->
                <div class="legal-notice" style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px; text-align: center;">
                    <p style="margin: 0; font-size: 14px; color: #666;">
                        <strong>ملاحظة قانونية:</strong> في حالة وجود تضارب بين النسختين العربية والإنجليزية، تعتبر النسخة العربية هي المرجع الأساسي.
                    </p>
                    <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                        <strong>Legal Notice:</strong> In case of any discrepancy between the Arabic and English versions, the Arabic version shall prevail.
                    </p>
                </div>
            </div>
            """

            print(f"✅ Bilingual content generated! Length: {len(bilingual_content)}")

            result = {
                'success': True,
                'content': bilingual_content,
                'arabic_content': arabic_content,
                'english_content': english_content
            }

            return jsonify(result)

        except Exception as e:
            print(f"❌ Bilingual generation error: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'message': f'خطأ في إنشاء العقد ثنائي اللغة: {str(e)}'}), 500

    except Exception as e:
        print(f"❌ General API error: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'حدث خطأ عام: {str(e)}'}), 500

@api_bp.route('/backup/create', methods=['POST'])
@login_required
def create_backup():
    """Create system backup"""
    if not current_user.is_manager():
        return jsonify({'success': False, 'message': 'غير مصرح لك بإنشاء نسخ احتياطية'})

    try:
        backup_service = BackupService()
        result = backup_service.create_full_backup()

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/backup/list', methods=['GET'])
@login_required
def list_backups():
    """List available backups"""
    if not current_user.is_manager():
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض النسخ الاحتياطية'})

    try:
        backup_service = BackupService()
        result = backup_service.get_backup_list()

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/qr/verify', methods=['POST'])
def verify_qr_code():
    """Verify QR code (public endpoint)"""
    data = request.get_json()
    qr_data = data.get('qr_data')

    if not qr_data:
        return jsonify({'success': False, 'message': 'بيانات QR Code مطلوبة'})

    try:
        qr_service = QRService()
        result = qr_service.verify_contract_qr(qr_data)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
