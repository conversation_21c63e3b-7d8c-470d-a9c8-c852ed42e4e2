from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
import json
import base64
from datetime import datetime
from models import db
from models.contract import Contract
from models.contract_type import ContractType
from models.notification import Notification
from utils.email_service import EmailService
from utils.whatsapp_service import WhatsAppService
from utils.qr_service import QRService
from utils.bilingual_service import BilingualService
from utils.backup_service import BackupService

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/contract_types')
@login_required
def get_contract_types():
    """Get all active contract types"""
    contract_types = ContractType.query.filter_by(is_active=True).all()
    return jsonify([ct.to_dict() for ct in contract_types])

@api_bp.route('/contract_type/<int:id>/template')
@login_required
def get_contract_template(id):
    """Get contract type template"""
    contract_type = ContractType.query.get_or_404(id)
    return jsonify({
        'template': contract_type.template,
        'fields': contract_type.fields
    })

@api_bp.route('/contracts/<int:id>/signature', methods=['POST'])
@login_required
def save_signature(id):
    """Save electronic signature for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتوقيع هذا العقد'})
    
    data = request.get_json()
    signature_type = data.get('type')  # first_party, second_party, company_seal
    signature_data = data.get('signature')  # Base64 encoded image
    
    if not signature_type or not signature_data:
        return jsonify({'success': False, 'message': 'بيانات التوقيع غير مكتملة'})
    
    try:
        # Validate signature type
        if signature_type == 'first_party':
            contract.first_party_signature = signature_data
        elif signature_type == 'second_party':
            contract.second_party_signature = signature_data
        elif signature_type == 'company_seal':
            contract.company_seal = signature_data
        else:
            return jsonify({'success': False, 'message': 'نوع التوقيع غير صحيح'})
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حفظ التوقيع بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ التوقيع'})

@api_bp.route('/contracts/<int:id>/attachments', methods=['POST'])
@login_required
def upload_attachment(id):
    """Upload file attachment for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتعديل هذا العقد'})
    
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    # Validate file type
    allowed_extensions = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
    if not ('.' in file.filename and 
            file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'})
    
    try:
        # Save file
        import os
        from werkzeug.utils import secure_filename
        
        filename = secure_filename(file.filename)
        upload_folder = os.path.join('static', 'uploads', 'contracts', str(contract.id))
        os.makedirs(upload_folder, exist_ok=True)
        
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)
        
        # Update contract attachments
        attachments = contract.attachments or []
        attachments.append({
            'filename': filename,
            'path': file_path,
            'uploaded_by': current_user.id,
            'uploaded_at': datetime.utcnow().isoformat()
        })
        contract.attachments = attachments
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم رفع الملف بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء رفع الملف'})

@api_bp.route('/contracts/search')
@login_required
def search_contracts():
    """Search contracts via API"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    # Build search query
    contracts_query = Contract.query
    
    # Filter by user role
    if not current_user.is_manager():
        contracts_query = contracts_query.filter_by(created_by=current_user.id)
    
    if query:
        from sqlalchemy import or_
        contracts_query = contracts_query.filter(
            or_(
                Contract.title.contains(query),
                Contract.first_party_name.contains(query),
                Contract.second_party_name.contains(query),
                Contract.contract_number.contains(query)
            )
        )
    
    contracts = contracts_query.limit(limit).all()
    return jsonify([contract.to_dict() for contract in contracts])

@api_bp.route('/contracts/<int:id>/preview')
@login_required
def preview_contract(id):
    """Get contract preview data"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not current_user.is_manager() and contract.created_by != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})
    
    # Process content with variables
    content = contract.content
    
    # Replace variables in content
    variables = {
        'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '',
        'first_party_name': contract.first_party_name,
        'second_party_name': contract.second_party_name,
        'contract_number': contract.contract_number,
        'contract_value': str(contract.contract_value) if contract.contract_value else '',
        'currency': contract.currency or 'QAR'
    }
    
    # Add custom fields
    if contract.custom_fields:
        variables.update(contract.custom_fields)
    
    # Replace variables in content
    for key, value in variables.items():
        content = content.replace(f'{{{{{key}}}}}', str(value))
    
    return jsonify({
        'success': True,
        'content': content,
        'contract': contract.to_dict()
    })

@api_bp.route('/notifications')
@login_required
def get_notifications():
    """Get user notifications"""
    notifications = Notification.get_recent_notifications(current_user.id, limit=20)
    unread_count = Notification.get_unread_count(current_user.id)

    return jsonify({
        'success': True,
        'notifications': [notification.to_dict() for notification in notifications],
        'unread_count': unread_count
    })

@api_bp.route('/notifications', methods=['POST'])
@login_required
def create_notification():
    """Create new notification"""
    data = request.get_json()

    title = data.get('title')
    message = data.get('message')
    notification_type = data.get('type', 'info')
    contract_id = data.get('contract_id')

    if not title or not message:
        return jsonify({'success': False, 'message': 'العنوان والرسالة مطلوبان'})

    try:
        notification = Notification(
            user_id=current_user.id,
            title=title,
            message=message,
            type=notification_type,
            contract_id=contract_id
        )

        db.session.add(notification)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء الإشعار بنجاح',
            'notification': notification.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء إنشاء الإشعار'})

@api_bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark notification as read"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first()

    if not notification:
        return jsonify({'success': False, 'message': 'الإشعار غير موجود'})

    try:
        notification.mark_as_read()
        return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
    except Exception as e:
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث الإشعار'})

@api_bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    try:
        notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).all()

        for notification in notifications:
            notification.is_read = True

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم تحديد {len(notifications)} إشعار كمقروء'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث الإشعارات'})

@api_bp.route('/contracts/<int:contract_id>/send-email', methods=['POST'])
@login_required
def send_contract_email(contract_id):
    """Send contract via email"""
    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json()
    recipient_email = data.get('email')
    recipient_name = data.get('name', 'العميل الكريم')
    include_pdf = data.get('include_pdf', True)
    include_word = data.get('include_word', False)
    custom_message = data.get('message')

    if not recipient_email:
        return jsonify({'success': False, 'message': 'البريد الإلكتروني مطلوب'})

    try:
        email_service = EmailService()
        result = email_service.send_contract_email(
            contract=contract,
            recipient_email=recipient_email,
            recipient_name=recipient_name,
            sent_by_user_id=current_user.id,
            include_pdf=include_pdf,
            include_word=include_word,
            custom_message=custom_message
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/contracts/<int:contract_id>/send-whatsapp', methods=['POST'])
@login_required
def send_contract_whatsapp(contract_id):
    """Send contract via WhatsApp"""
    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json()
    recipient_phone = data.get('phone')
    recipient_name = data.get('name', 'العميل الكريم')
    include_pdf = data.get('include_pdf', True)
    custom_message = data.get('message')

    if not recipient_phone:
        return jsonify({'success': False, 'message': 'رقم الهاتف مطلوب'})

    try:
        whatsapp_service = WhatsAppService()
        result = whatsapp_service.send_contract_whatsapp(
            contract=contract,
            recipient_phone=recipient_phone,
            recipient_name=recipient_name,
            sent_by_user_id=current_user.id,
            include_pdf=include_pdf,
            custom_message=custom_message
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/contracts/<int:contract_id>/generate-qr', methods=['POST'])
@login_required
def generate_contract_qr(contract_id):
    """Generate QR code for contract"""
    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json() or {}
    include_verification = data.get('include_verification', True)

    try:
        qr_service = QRService()
        result = qr_service.generate_contract_qr(contract, include_verification)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/contracts/<int:contract_id>/bilingual', methods=['POST'])
@login_required
def generate_bilingual_contract(contract_id):
    """Generate bilingual contract"""
    contract = Contract.query.get_or_404(contract_id)

    if not contract.can_view(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})

    data = request.get_json() or {}
    include_english = data.get('include_english', True)

    try:
        bilingual_service = BilingualService()
        result = bilingual_service.create_bilingual_contract(contract, include_english)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/backup/create', methods=['POST'])
@login_required
def create_backup():
    """Create system backup"""
    if not current_user.is_manager():
        return jsonify({'success': False, 'message': 'غير مصرح لك بإنشاء نسخ احتياطية'})

    try:
        backup_service = BackupService()
        result = backup_service.create_full_backup()

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/backup/list', methods=['GET'])
@login_required
def list_backups():
    """List available backups"""
    if not current_user.is_manager():
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض النسخ الاحتياطية'})

    try:
        backup_service = BackupService()
        result = backup_service.get_backup_list()

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@api_bp.route('/qr/verify', methods=['POST'])
def verify_qr_code():
    """Verify QR code (public endpoint)"""
    data = request.get_json()
    qr_data = data.get('qr_data')

    if not qr_data:
        return jsonify({'success': False, 'message': 'بيانات QR Code مطلوبة'})

    try:
        qr_service = QRService()
        result = qr_service.verify_contract_qr(qr_data)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
