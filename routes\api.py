from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
import json
import base64
from datetime import datetime
from models import db
from models.contract import Contract
from models.contract_type import ContractType

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/contract_types')
@login_required
def get_contract_types():
    """Get all active contract types"""
    contract_types = ContractType.query.filter_by(is_active=True).all()
    return jsonify([ct.to_dict() for ct in contract_types])

@api_bp.route('/contract_type/<int:id>/template')
@login_required
def get_contract_template(id):
    """Get contract type template"""
    contract_type = ContractType.query.get_or_404(id)
    return jsonify({
        'template': contract_type.template,
        'fields': contract_type.fields
    })

@api_bp.route('/contracts/<int:id>/signature', methods=['POST'])
@login_required
def save_signature(id):
    """Save electronic signature for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتوقيع هذا العقد'})
    
    data = request.get_json()
    signature_type = data.get('type')  # first_party, second_party, company_seal
    signature_data = data.get('signature')  # Base64 encoded image
    
    if not signature_type or not signature_data:
        return jsonify({'success': False, 'message': 'بيانات التوقيع غير مكتملة'})
    
    try:
        # Validate signature type
        if signature_type == 'first_party':
            contract.first_party_signature = signature_data
        elif signature_type == 'second_party':
            contract.second_party_signature = signature_data
        elif signature_type == 'company_seal':
            contract.company_seal = signature_data
        else:
            return jsonify({'success': False, 'message': 'نوع التوقيع غير صحيح'})
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حفظ التوقيع بنجاح'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ التوقيع'})

@api_bp.route('/contracts/<int:id>/attachments', methods=['POST'])
@login_required
def upload_attachment(id):
    """Upload file attachment for contract"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not contract.can_edit(current_user):
        return jsonify({'success': False, 'message': 'غير مصرح لك بتعديل هذا العقد'})
    
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})
    
    # Validate file type
    allowed_extensions = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
    if not ('.' in file.filename and 
            file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'})
    
    try:
        # Save file
        import os
        from werkzeug.utils import secure_filename
        
        filename = secure_filename(file.filename)
        upload_folder = os.path.join('static', 'uploads', 'contracts', str(contract.id))
        os.makedirs(upload_folder, exist_ok=True)
        
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)
        
        # Update contract attachments
        attachments = contract.attachments or []
        attachments.append({
            'filename': filename,
            'path': file_path,
            'uploaded_by': current_user.id,
            'uploaded_at': datetime.utcnow().isoformat()
        })
        contract.attachments = attachments
        
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم رفع الملف بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء رفع الملف'})

@api_bp.route('/contracts/search')
@login_required
def search_contracts():
    """Search contracts via API"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    # Build search query
    contracts_query = Contract.query
    
    # Filter by user role
    if not current_user.is_manager():
        contracts_query = contracts_query.filter_by(created_by=current_user.id)
    
    if query:
        from sqlalchemy import or_
        contracts_query = contracts_query.filter(
            or_(
                Contract.title.contains(query),
                Contract.first_party_name.contains(query),
                Contract.second_party_name.contains(query),
                Contract.contract_number.contains(query)
            )
        )
    
    contracts = contracts_query.limit(limit).all()
    return jsonify([contract.to_dict() for contract in contracts])

@api_bp.route('/contracts/<int:id>/preview')
@login_required
def preview_contract(id):
    """Get contract preview data"""
    contract = Contract.query.get_or_404(id)
    
    # Check permissions
    if not current_user.is_manager() and contract.created_by != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح لك بعرض هذا العقد'})
    
    # Process content with variables
    content = contract.content
    
    # Replace variables in content
    variables = {
        'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '',
        'first_party_name': contract.first_party_name,
        'second_party_name': contract.second_party_name,
        'contract_number': contract.contract_number,
        'contract_value': str(contract.contract_value) if contract.contract_value else '',
        'currency': contract.currency
    }
    
    # Add custom fields
    if contract.custom_fields:
        variables.update(contract.custom_fields)
    
    # Replace variables in content
    for key, value in variables.items():
        content = content.replace(f'{{{{{key}}}}}', str(value))
    
    return jsonify({
        'success': True,
        'content': content,
        'contract': contract.to_dict()
    })
