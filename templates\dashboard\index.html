{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </h1>
                <div>
                    <a href="{{ url_for('contracts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء عقد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي العقود
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_contracts }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-contract fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                العقود النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_contracts }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                المسودات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.draft_contracts }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                هذا الشهر
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.contracts_this_month }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Content Row -->
    <div class="row">
        <!-- Recent Contracts -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">العقود الحديثة</h6>
                    <a href="{{ url_for('contracts.index') }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_contracts %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>رقم العقد</th>
                                        <th>العنوان</th>
                                        <th>الطرف الأول</th>
                                        <th>الطرف الثاني</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for contract in recent_contracts %}
                                    <tr>
                                        <td>{{ contract.contract_number }}</td>
                                        <td>{{ contract.title }}</td>
                                        <td>{{ contract.first_party_name }}</td>
                                        <td>{{ contract.second_party_name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if contract.status == 'active' else 'warning' if contract.status == 'draft' else 'secondary' }}">
                                                {{ contract.get_status_display() }}
                                            </span>
                                        </td>
                                        <td>{{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '' }}</td>
                                        <td>
                                            <a href="{{ url_for('contracts.view', id=contract.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-file-contract fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">لا توجد عقود حديثة</p>
                            <a href="{{ url_for('contracts.create') }}" class="btn btn-primary">
                                إنشاء عقد جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Contract Status Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع العقود حسب الحالة</h6>
                </div>
                <div class="card-body">
                    {% if contracts_by_status %}
                        {% for status, count in contracts_by_status %}
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>{{ status }}</span>
                                <span class="font-weight-bold">{{ count }}</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-{{ 'success' if status == 'active' else 'warning' if status == 'draft' else 'secondary' }}" 
                                     style="width: {{ (count / stats.total_contracts * 100) if stats.total_contracts > 0 else 0 }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}

.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}

.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}

.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
