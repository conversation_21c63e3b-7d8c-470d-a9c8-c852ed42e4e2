#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test direct imports and functionality
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Test all imports"""
    print("🔍 Testing imports...")
    
    try:
        print("   Testing qrcode...")
        import qrcode
        from qrcode.constants import ERROR_CORRECT_L
        print("   ✅ qrcode imported successfully")
    except Exception as e:
        print(f"   ❌ qrcode import failed: {e}")
        return False
    
    try:
        print("   Testing PIL...")
        from PIL import Image
        print("   ✅ PIL imported successfully")
    except Exception as e:
        print(f"   ❌ PIL import failed: {e}")
        return False
    
    try:
        print("   Testing utils.qr_service...")
        from utils.qr_service import QRService
        print("   ✅ QRService imported successfully")
    except Exception as e:
        print(f"   ❌ QRService import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("   Testing utils.bilingual_service...")
        from utils.bilingual_service import BilingualService
        print("   ✅ BilingualService imported successfully")
    except Exception as e:
        print(f"   ❌ BilingualService import failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_qr_creation():
    """Test QR code creation directly"""
    print("\n🔍 Testing QR creation...")
    
    try:
        from utils.qr_service import QRService
        import os
        
        # Create mock contract
        class MockContract:
            def __init__(self):
                self.id = 999
                self.contract_number = "TEST-999"
                self.title = "اختبار QR"
                self.first_party_name = "الطرف الأول"
                self.second_party_name = "الطرف الثاني"
                self.contract_date = datetime.now().date()
                self.contract_value = 1000
                self.currency = "QAR"
                self.status = "active"
                self.created_at = datetime.now()
        
        contract = MockContract()
        
        # Test QR service
        qr_service = QRService()
        print(f"   QR directory: {qr_service.qr_dir}")
        
        # Check if directory exists
        if not os.path.exists(qr_service.qr_dir):
            print(f"   Creating QR directory: {qr_service.qr_dir}")
            os.makedirs(qr_service.qr_dir, exist_ok=True)
        
        result = qr_service.generate_contract_qr(contract, include_verification=True)
        
        if result['success']:
            print("   ✅ QR Code created successfully!")
            print(f"   📊 Base64 length: {len(result['base64'])}")
            return True
        else:
            print(f"   ❌ QR Code creation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ QR creation error: {e}")
        traceback.print_exc()
        return False

def test_bilingual_creation():
    """Test bilingual contract creation directly"""
    print("\n🌐 Testing bilingual creation...")
    
    try:
        from utils.bilingual_service import BilingualService
        
        # Create mock contract
        class MockContractType:
            def __init__(self):
                self.name = "بيع سيارة"
        
        class MockContract:
            def __init__(self):
                self.id = 999
                self.contract_number = "TEST-999"
                self.title = "اختبار ثنائي اللغة"
                self.first_party_name = "أحمد محمد"
                self.second_party_name = "شركة قطر"
                self.contract_date = datetime.now().date()
                self.contract_value = 50000
                self.currency = "QAR"
                self.content = "محتوى العقد التجريبي"
                self.terms_conditions = "الشروط والأحكام"
                self.contract_type = MockContractType()
        
        contract = MockContract()
        
        # Test bilingual service
        bilingual_service = BilingualService()
        result = bilingual_service.create_bilingual_contract(contract, include_english=True)
        
        if result['success']:
            print("   ✅ Bilingual contract created successfully!")
            print(f"   📊 Content length: {len(result['content'])}")
            return True
        else:
            print(f"   ❌ Bilingual creation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ Bilingual creation error: {e}")
        traceback.print_exc()
        return False

def test_with_flask_app():
    """Test with Flask app context"""
    print("\n🌐 Testing with Flask app...")
    
    try:
        from app import create_app
        from models.contract import Contract
        
        app = create_app()
        
        with app.app_context():
            # Get first contract
            contract = Contract.query.first()
            
            if not contract:
                print("   ❌ No contracts found in database")
                return False
            
            print(f"   📋 Using contract: {contract.contract_number}")
            
            # Test QR with real contract
            try:
                from utils.qr_service import QRService
                qr_service = QRService()
                qr_result = qr_service.generate_contract_qr(contract, include_verification=True)
                
                if qr_result['success']:
                    print("   ✅ QR with real contract works!")
                else:
                    print(f"   ❌ QR with real contract failed: {qr_result['message']}")
            except Exception as e:
                print(f"   ❌ QR with real contract error: {e}")
                traceback.print_exc()
            
            # Test bilingual with real contract
            try:
                from utils.bilingual_service import BilingualService
                bilingual_service = BilingualService()
                bilingual_result = bilingual_service.create_bilingual_contract(contract, include_english=True)
                
                if bilingual_result['success']:
                    print("   ✅ Bilingual with real contract works!")
                else:
                    print(f"   ❌ Bilingual with real contract failed: {bilingual_result['message']}")
            except Exception as e:
                print(f"   ❌ Bilingual with real contract error: {e}")
                traceback.print_exc()
            
            return True
            
    except Exception as e:
        print(f"   ❌ Flask app test error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🐛 Direct Import and Functionality Test")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    if not imports_ok:
        print("\n❌ Import tests failed. Cannot proceed.")
        return
    
    # Test QR creation
    qr_ok = test_qr_creation()
    
    # Test bilingual creation
    bilingual_ok = test_bilingual_creation()
    
    # Test with Flask app
    flask_ok = test_with_flask_app()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"   Imports: {'✅ OK' if imports_ok else '❌ FAILED'}")
    print(f"   QR Creation: {'✅ OK' if qr_ok else '❌ FAILED'}")
    print(f"   Bilingual Creation: {'✅ OK' if bilingual_ok else '❌ FAILED'}")
    print(f"   Flask App Test: {'✅ OK' if flask_ok else '❌ FAILED'}")
    
    if imports_ok and qr_ok and bilingual_ok and flask_ok:
        print("\n🎉 All tests passed! The issue might be in the API routes.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")

if __name__ == '__main__':
    main()
