#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test QR Code generation
"""

def test_qr_generation():
    """Test QR code generation"""
    try:
        import qrcode
        from qrcode.constants import ERROR_CORRECT_L
        from io import BytesIO
        import base64
        
        # Create simple QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data("http://localhost:5000/verify/TEST-001")
        qr.make(fit=True)
        
        # Create QR code image
        qr_image = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        qr_image.save(buffer, format='PNG')
        qr_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        print("✅ QR Code generation successful!")
        print(f"Base64 length: {len(qr_base64)}")
        return True
        
    except ImportError as e:
        print(f"❌ QR Code library not available: {e}")
        print("Please install: pip install qrcode[pil]")
        return False
    except Exception as e:
        print(f"❌ QR Code generation failed: {e}")
        return False

def test_bilingual_service():
    """Test bilingual service"""
    try:
        from utils.bilingual_service import BilingualService
        from datetime import datetime
        
        # Mock contract object
        class MockContract:
            def __init__(self):
                self.id = 1
                self.contract_number = "TEST-001"
                self.title = "عقد اختبار"
                self.first_party_name = "الطرف الأول"
                self.second_party_name = "الطرف الثاني"
                self.contract_date = datetime.now().date()
                self.contract_value = 10000
                self.currency = "QAR"
                self.content = "محتوى العقد التجريبي"
                self.terms_conditions = "الشروط والأحكام"
                self.contract_type = None
        
        contract = MockContract()
        bilingual_service = BilingualService()
        
        result = bilingual_service.create_bilingual_contract(contract, include_english=True)
        
        if result['success']:
            print("✅ Bilingual contract generation successful!")
            print(f"Content length: {len(result['content'])}")
            return True
        else:
            print(f"❌ Bilingual contract generation failed: {result.get('message', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Bilingual service not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Bilingual service test failed: {e}")
        return False

if __name__ == '__main__':
    print("🧪 Testing QR Code and Bilingual Services...")
    print("=" * 50)
    
    qr_success = test_qr_generation()
    print()
    
    bilingual_success = test_bilingual_service()
    print()
    
    if qr_success and bilingual_success:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        
        if not qr_success:
            print("\n📦 To fix QR Code issues, run:")
            print("pip install qrcode[pil]")
        
        if not bilingual_success:
            print("\n🔧 Check bilingual service implementation")
