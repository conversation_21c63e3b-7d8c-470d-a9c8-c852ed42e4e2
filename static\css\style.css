/* Arabic RTL Support and Custom Styles */

* {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
}

body {
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

/* RTL Adjustments */
.navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 1rem;
}

.dropdown-menu {
    right: 0;
    left: auto;
}

/* Custom Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 15px 15px 0 0 !important;
}

/* Button Styles */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

/* Badge Styles */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* Contract Editor Styles */
.contract-editor {
    min-height: 400px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
}

.contract-editor .ql-toolbar {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: 1px solid #e9ecef;
}

.contract-editor .ql-container {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    min-height: 350px;
}

/* Signature Pad Styles */
.signature-pad {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background-color: white;
    cursor: crosshair;
}

.signature-controls {
    margin-top: 10px;
    text-align: center;
}

/* File Upload Styles */
.file-upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.file-upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
}

/* Contract Preview Styles */
.contract-preview {
    background-color: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin: 20px 0;
}

.contract-preview h1, .contract-preview h2, .contract-preview h3 {
    color: #333;
    margin-bottom: 20px;
}

.contract-preview .parties-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.contract-preview .signature-section {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 2px solid #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 14px;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Print Styles */
@media print {
    .navbar, .btn, .card-header, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .contract-preview {
        box-shadow: none !important;
        margin: 0 !important;
        padding: 20px !important;
    }
    
    body {
        background-color: white !important;
    }
}
