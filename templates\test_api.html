<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API - المميزات المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🧪 اختبار API - المميزات المتقدمة</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-qrcode me-2"></i>اختبار QR Code</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testQRCode()">
                            <i class="fas fa-play me-1"></i>
                            اختبار QR Code
                        </button>
                        <div id="qrResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-language me-2"></i>اختبار العقد ثنائي اللغة</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" onclick="testBilingual()">
                            <i class="fas fa-play me-1"></i>
                            اختبار ثنائي اللغة
                        </button>
                        <div id="bilingualResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>العقود المتاحة</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info" onclick="loadContracts()">
                            <i class="fas fa-refresh me-1"></i>
                            تحميل العقود
                        </button>
                        <div id="contractsList" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedContractId = null;
        
        // Load available contracts
        function loadContracts() {
            document.getElementById('contractsList').innerHTML = '<div class="spinner-border" role="status"></div>';
            
            fetch('/api/contracts')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.contracts.length > 0) {
                    selectedContractId = data.contracts[0].id;
                    let html = '<div class="row">';
                    data.contracts.forEach(contract => {
                        html += `
                            <div class="col-md-4 mb-2">
                                <div class="card ${contract.id === selectedContractId ? 'border-primary' : ''}">
                                    <div class="card-body p-2">
                                        <h6 class="card-title">${contract.contract_number}</h6>
                                        <p class="card-text small">${contract.title}</p>
                                        <button class="btn btn-sm btn-outline-primary" onclick="selectContract(${contract.id})">
                                            اختيار
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    html += `<div class="alert alert-info mt-3">العقد المحدد: ${data.contracts[0].contract_number}</div>`;
                    document.getElementById('contractsList').innerHTML = html;
                } else {
                    document.getElementById('contractsList').innerHTML = '<div class="alert alert-warning">لا توجد عقود متاحة</div>';
                }
            })
            .catch(error => {
                document.getElementById('contractsList').innerHTML = '<div class="alert alert-danger">خطأ في تحميل العقود</div>';
            });
        }
        
        function selectContract(contractId) {
            selectedContractId = contractId;
            loadContracts(); // Refresh to show selection
        }
        
        // Test QR Code generation
        function testQRCode() {
            if (!selectedContractId) {
                document.getElementById('qrResult').innerHTML = '<div class="alert alert-warning">يرجى اختيار عقد أولاً</div>';
                return;
            }
            
            document.getElementById('qrResult').innerHTML = '<div class="spinner-border" role="status"></div>';
            
            fetch(`/api/contracts/${selectedContractId}/generate-qr`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ include_verification: true })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    document.getElementById('qrResult').innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ تم إنشاء QR Code بنجاح!</h6>
                            <img src="data:image/png;base64,${result.base64}" class="img-fluid" style="max-width: 200px;">
                            <p class="mt-2 small">رمز التحقق: ${result.verification_hash || 'غير متوفر'}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('qrResult').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ فشل في إنشاء QR Code</h6>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('qrResult').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ خطأ في الشبكة</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
        
        // Test Bilingual contract generation
        function testBilingual() {
            if (!selectedContractId) {
                document.getElementById('bilingualResult').innerHTML = '<div class="alert alert-warning">يرجى اختيار عقد أولاً</div>';
                return;
            }
            
            document.getElementById('bilingualResult').innerHTML = '<div class="spinner-border" role="status"></div>';
            
            fetch(`/api/contracts/${selectedContractId}/bilingual`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ include_english: true })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    document.getElementById('bilingualResult').innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ تم إنشاء العقد ثنائي اللغة بنجاح!</h6>
                            <p>طول المحتوى: ${result.content.length} حرف</p>
                            <button class="btn btn-sm btn-primary" onclick="showBilingualPreview('${btoa(result.content)}')">
                                معاينة
                            </button>
                        </div>
                    `;
                } else {
                    document.getElementById('bilingualResult').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ فشل في إنشاء العقد ثنائي اللغة</h6>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('bilingualResult').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ خطأ في الشبكة</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
        
        function showBilingualPreview(encodedContent) {
            const content = atob(encodedContent);
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>معاينة العقد ثنائي اللغة</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                </head>
                <body>
                    <div class="container my-4">
                        <button onclick="window.close()" class="btn btn-secondary mb-3">إغلاق</button>
                        ${content}
                    </div>
                </body>
                </html>
            `);
            newWindow.document.close();
        }
        
        // Load contracts on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadContracts();
        });
    </script>
</body>
</html>
