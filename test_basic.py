#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic test without complex imports
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_basic_qr():
    """Test basic QR functionality"""
    print("🔍 Testing basic QR...")
    
    try:
        import qrcode
        from io import BytesIO
        import base64
        
        # Create simple QR
        qr = qrcode.QRCode(version=1, box_size=10, border=4)
        qr.add_data("http://localhost:5000/verify/TEST-001")
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        b64 = base64.b64encode(buffer.getvalue()).decode()
        
        print(f"   ✅ QR created! Base64 length: {len(b64)}")
        return True
        
    except Exception as e:
        print(f"   ❌ QR test failed: {e}")
        return False

def test_basic_html():
    """Test basic HTML generation"""
    print("\n📝 Testing basic HTML...")
    
    try:
        html_content = """
        <div class="contract">
            <h1>عقد اختبار</h1>
            <p>هذا عقد تجريبي</p>
            <div class="english">
                <h1>Test Contract</h1>
                <p>This is a test contract</p>
            </div>
        </div>
        """
        
        print(f"   ✅ HTML created! Length: {len(html_content)}")
        return True
        
    except Exception as e:
        print(f"   ❌ HTML test failed: {e}")
        return False

def test_imports():
    """Test required imports"""
    print("\n📦 Testing imports...")
    
    imports = [
        ('qrcode', 'QR Code library'),
        ('PIL', 'Pillow library'),
        ('base64', 'Base64 encoding'),
        ('json', 'JSON handling'),
        ('datetime', 'Date handling'),
    ]
    
    all_ok = True
    for module, desc in imports:
        try:
            __import__(module)
            print(f"   ✅ {desc}")
        except ImportError:
            print(f"   ❌ {desc} - MISSING")
            all_ok = False
    
    return all_ok

if __name__ == '__main__':
    print("🧪 Basic Functionality Test")
    print("=" * 40)
    
    imports_ok = test_imports()
    qr_ok = test_basic_qr()
    html_ok = test_basic_html()
    
    print("\n" + "=" * 40)
    print("📊 RESULTS:")
    print(f"   Imports: {'✅' if imports_ok else '❌'}")
    print(f"   QR Code: {'✅' if qr_ok else '❌'}")
    print(f"   HTML: {'✅' if html_ok else '❌'}")
    
    if imports_ok and qr_ok and html_ok:
        print("\n🎉 All basic tests passed!")
        print("The issue might be in the Flask app or API routes.")
    else:
        print("\n⚠️ Some basic tests failed.")
        if not imports_ok:
            print("   Install missing libraries: pip install qrcode[pil]")
        if not qr_ok:
            print("   QR Code generation has issues")
        if not html_ok:
            print("   HTML generation has issues")
