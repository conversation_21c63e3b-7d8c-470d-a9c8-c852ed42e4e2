import base64
import os
from PIL import Image
from io import BytesIO

def process_signature(signature_data):
    """Process and validate signature data"""
    try:
        # Remove data URL prefix if present
        if signature_data.startswith('data:image'):
            signature_data = signature_data.split(',')[1]
        
        # Decode base64 data
        image_data = base64.b64decode(signature_data)
        
        # Validate image
        image = Image.open(BytesIO(image_data))
        
        # Convert to PNG if not already
        if image.format != 'PNG':
            output = BytesIO()
            image.save(output, format='PNG')
            image_data = output.getvalue()
            signature_data = base64.b64encode(image_data).decode('utf-8')
        
        return signature_data
        
    except Exception as e:
        raise ValueError(f"Invalid signature data: {e}")

def save_signature_file(signature_data, contract_id, signature_type):
    """Save signature as file"""
    try:
        # Process signature
        processed_signature = process_signature(signature_data)
        
        # Create directory
        signature_dir = os.path.join('static', 'uploads', 'signatures', str(contract_id))
        os.makedirs(signature_dir, exist_ok=True)
        
        # Save file
        filename = f"{signature_type}.png"
        file_path = os.path.join(signature_dir, filename)
        
        # Decode and save
        image_data = base64.b64decode(processed_signature)
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        return file_path
        
    except Exception as e:
        raise ValueError(f"Failed to save signature: {e}")

def get_signature_image_tag(signature_data, width=200, height=100):
    """Get HTML img tag for signature"""
    if not signature_data:
        return ""
    
    try:
        # Ensure proper data URL format
        if not signature_data.startswith('data:image'):
            signature_data = f"data:image/png;base64,{signature_data}"
        
        return f'<img src="{signature_data}" width="{width}" height="{height}" style="border: 1px solid #ccc;" />'
        
    except Exception:
        return ""

def validate_signature_size(signature_data, max_size_mb=2):
    """Validate signature file size"""
    try:
        # Calculate size
        if signature_data.startswith('data:image'):
            signature_data = signature_data.split(',')[1]
        
        size_bytes = len(base64.b64decode(signature_data))
        size_mb = size_bytes / (1024 * 1024)
        
        if size_mb > max_size_mb:
            raise ValueError(f"Signature size ({size_mb:.2f}MB) exceeds maximum allowed size ({max_size_mb}MB)")
        
        return True
        
    except Exception as e:
        raise ValueError(f"Invalid signature: {e}")

def create_signature_placeholder(width=200, height=100):
    """Create a placeholder signature image"""
    from PIL import Image, ImageDraw, ImageFont
    
    # Create blank image
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # Draw border
    draw.rectangle([0, 0, width-1, height-1], outline='black', width=2)
    
    # Add text
    try:
        # Try to use a font
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    text = "توقيع"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    draw.text((x, y), text, fill='gray', font=font)
    
    # Convert to base64
    output = BytesIO()
    image.save(output, format='PNG')
    image_data = output.getvalue()
    
    return base64.b64encode(image_data).decode('utf-8')

def merge_signatures_with_contract(contract_html, signatures):
    """Merge signatures with contract HTML"""
    try:
        # Replace signature placeholders in HTML
        for sig_type, sig_data in signatures.items():
            if sig_data:
                img_tag = get_signature_image_tag(sig_data)
                placeholder = f"{{{{signature_{sig_type}}}}}"
                contract_html = contract_html.replace(placeholder, img_tag)
            else:
                # Use placeholder
                placeholder_data = create_signature_placeholder()
                img_tag = get_signature_image_tag(placeholder_data)
                placeholder = f"{{{{signature_{sig_type}}}}}"
                contract_html = contract_html.replace(placeholder, img_tag)
        
        return contract_html
        
    except Exception as e:
        raise ValueError(f"Failed to merge signatures: {e}")
