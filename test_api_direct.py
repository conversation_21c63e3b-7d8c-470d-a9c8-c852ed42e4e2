#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Direct API test
"""

import requests
import json

def test_api_endpoints():
    """Test API endpoints directly"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing API endpoints directly...")
    
    # Test QR Code generation
    print("\n1. Testing QR Code generation...")
    try:
        response = requests.post(
            f"{base_url}/api/contracts/1/generate-qr",
            json={"include_verification": True},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ QR Code API works!")
                print(f"   📊 Base64 length: {len(result.get('base64', ''))}")
            else:
                print(f"   ❌ QR Code API failed: {result.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test Bilingual generation
    print("\n2. Testing Bilingual generation...")
    try:
        response = requests.post(
            f"{base_url}/api/contracts/1/bilingual",
            json={"include_english": True},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ Bilingual API works!")
                print(f"   📊 Content length: {len(result.get('content', ''))}")
            else:
                print(f"   ❌ Bilingual API failed: {result.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test contracts list
    print("\n3. Testing contracts list...")
    try:
        response = requests.get(f"{base_url}/api/contracts")
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Contracts API works! Found {len(result.get('contracts', []))} contracts")
            else:
                print(f"   ❌ Contracts API failed: {result.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")

if __name__ == '__main__':
    test_api_endpoints()
