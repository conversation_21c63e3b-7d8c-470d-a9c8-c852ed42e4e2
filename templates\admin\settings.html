{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات النظام
                </h1>
            </div>
        </div>
    </div>
    
    <!-- Settings Tabs -->
    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                        <i class="fas fa-envelope me-1"></i>
                        البريد الإلكتروني
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="whatsapp-tab" data-bs-toggle="tab" data-bs-target="#whatsapp" type="button" role="tab">
                        <i class="fab fa-whatsapp me-1"></i>
                        WhatsApp
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                        <i class="fas fa-database me-1"></i>
                        النسخ الاحتياطي
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="fas fa-server me-1"></i>
                        النظام
                    </button>
                </li>
            </ul>
        </div>
        
        <div class="card-body">
            <div class="tab-content" id="settingsTabContent">
                <!-- Email Settings -->
                <div class="tab-pane fade show active" id="email" role="tabpanel">
                    <h5 class="mb-4">إعدادات البريد الإلكتروني</h5>
                    
                    <form id="emailSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtpServer" class="form-label">خادم SMTP *</label>
                                    <input type="text" class="form-control" id="smtpServer" 
                                           placeholder="smtp.gmail.com" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtpPort" class="form-label">منفذ SMTP *</label>
                                    <input type="number" class="form-control" id="smtpPort" 
                                           value="587" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtpUsername" class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="smtpUsername" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtpPassword" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="smtpPassword" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fromEmail" class="form-label">البريد المرسل *</label>
                                    <input type="email" class="form-control" id="fromEmail" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fromName" class="form-label">اسم المرسل *</label>
                                    <input type="text" class="form-control" id="fromName" 
                                           value="نظام إدارة العقود" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="useTLS" checked>
                                <label class="form-check-label" for="useTLS">
                                    استخدام TLS
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ الإعدادات
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="testEmailConnection()">
                                <i class="fas fa-plug me-1"></i>
                                اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- WhatsApp Settings -->
                <div class="tab-pane fade" id="whatsapp" role="tabpanel">
                    <h5 class="mb-4">إعدادات WhatsApp Business API</h5>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تحتاج إلى حساب WhatsApp Business API للاستفادة من هذه الميزة
                    </div>
                    
                    <form id="whatsappSettingsForm">
                        <div class="mb-3">
                            <label for="apiUrl" class="form-label">رابط API *</label>
                            <input type="url" class="form-control" id="apiUrl" 
                                   placeholder="https://graph.facebook.com/v17.0" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="apiToken" class="form-label">رمز API *</label>
                            <input type="password" class="form-control" id="apiToken" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phoneNumberId" class="form-label">معرف رقم الهاتف *</label>
                                    <input type="text" class="form-control" id="phoneNumberId" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="businessAccountId" class="form-label">معرف الحساب التجاري *</label>
                                    <input type="text" class="form-control" id="businessAccountId" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                حفظ الإعدادات
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="testWhatsAppConnection()">
                                <i class="fas fa-plug me-1"></i>
                                اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Backup Settings -->
                <div class="tab-pane fade" id="backup" role="tabpanel">
                    <h5 class="mb-4">إعدادات النسخ الاحتياطي</h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0">النسخ الاحتياطي التلقائي</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                            <label class="form-check-label" for="autoBackup">
                                                تفعيل النسخ الاحتياطي التلقائي (يومياً في الساعة 2:00 صباحاً)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="maxBackups" class="form-label">عدد النسخ الاحتياطية المحفوظة</label>
                                        <input type="number" class="form-control" id="maxBackups" value="30" min="1" max="365">
                                        <div class="form-text">سيتم حذف النسخ الأقدم تلقائياً</div>
                                    </div>
                                    
                                    <button type="button" class="btn btn-primary" onclick="createBackup()">
                                        <i class="fas fa-database me-1"></i>
                                        إنشاء نسخة احتياطية الآن
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0">النسخ الاحتياطية المتاحة</h6>
                                </div>
                                <div class="card-body">
                                    <div id="backupsList">
                                        <div class="text-center">
                                            <div class="spinner-border spinner-border-sm" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Settings -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <h5 class="mb-4">معلومات النظام</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0">إحصائيات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>إجمالي العقود:</strong></td>
                                            <td id="totalContracts">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>العقود النشطة:</strong></td>
                                            <td id="activeContracts">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المستخدمين:</strong></td>
                                            <td id="totalUsers">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>أنواع العقود:</strong></td>
                                            <td id="contractTypes">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0">معلومات تقنية</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>إصدار النظام:</strong></td>
                                            <td>2.0.0</td>
                                        </tr>
                                        <tr>
                                            <td><strong>قاعدة البيانات:</strong></td>
                                            <td>SQLite</td>
                                        </tr>
                                        <tr>
                                            <td><strong>آخر نسخة احتياطية:</strong></td>
                                            <td id="lastBackup">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>مساحة التخزين:</strong></td>
                                            <td id="storageUsed">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="m-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    منطقة الخطر
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">
                                    هذه الإجراءات لا يمكن التراجع عنها. يرجى التأكد من إنشاء نسخة احتياطية قبل المتابعة.
                                </p>
                                <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">
                                    <i class="fas fa-trash me-1"></i>
                                    مسح سجلات النظام
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load settings on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSystemStats();
    loadBackupsList();
});

// System statistics
function loadSystemStats() {
    // This would typically fetch from an API endpoint
    // For now, we'll use placeholder values
    document.getElementById('totalContracts').textContent = '{{ total_contracts or "-" }}';
    document.getElementById('activeContracts').textContent = '{{ active_contracts or "-" }}';
    document.getElementById('totalUsers').textContent = '{{ total_users or "-" }}';
    document.getElementById('contractTypes').textContent = '{{ contract_types or "-" }}';
}

// Email settings
document.getElementById('emailSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Save email settings
    showAlert('تم حفظ إعدادات البريد الإلكتروني', 'success');
});

function testEmailConnection() {
    showAlert('جاري اختبار الاتصال...', 'info');
    // Test email connection
    setTimeout(() => {
        showAlert('تم الاتصال بخادم البريد الإلكتروني بنجاح', 'success');
    }, 2000);
}

// WhatsApp settings
document.getElementById('whatsappSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Save WhatsApp settings
    showAlert('تم حفظ إعدادات WhatsApp', 'success');
});

function testWhatsAppConnection() {
    showAlert('جاري اختبار الاتصال...', 'info');
    // Test WhatsApp connection
    setTimeout(() => {
        showAlert('تم الاتصال بـ WhatsApp API بنجاح', 'success');
    }, 2000);
}

// Backup functions
function createBackup() {
    showAlert('جاري إنشاء النسخة الاحتياطية...', 'info');
    
    fetch('/api/backup/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            loadBackupsList();
        } else {
            showAlert(result.message || 'فشل في إنشاء النسخة الاحتياطية', 'error');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
    });
}

function loadBackupsList() {
    fetch('/api/backup/list')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const backupsList = document.getElementById('backupsList');
            if (result.backups.length === 0) {
                backupsList.innerHTML = '<p class="text-muted text-center">لا توجد نسخ احتياطية</p>';
            } else {
                let html = '';
                result.backups.slice(0, 5).forEach(backup => {
                    html += `
                        <div class="backup-item mb-2 p-2 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">${new Date(backup.created_at).toLocaleDateString('ar-QA')}</small>
                                    <br>
                                    <small>${backup.size_mb} MB</small>
                                </div>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${backup.filename}')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                backupsList.innerHTML = html;
            }
        }
    })
    .catch(error => {
        document.getElementById('backupsList').innerHTML = '<p class="text-danger text-center">خطأ في تحميل القائمة</p>';
    });
}

function downloadBackup(filename) {
    window.location.href = `/api/backup/download/${filename}`;
}

function clearLogs() {
    if (confirm('هل أنت متأكد من مسح جميع سجلات النظام؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        showAlert('تم مسح سجلات النظام', 'success');
    }
}
</script>
{% endblock %}
