#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to test services directly
"""

import sys
import traceback
from datetime import datetime

def test_qr_service():
    """Test QR service directly"""
    print("🔍 Testing QR Service...")
    try:
        from utils.qr_service import QRService
        
        # Create mock contract
        class MockContract:
            def __init__(self):
                self.id = 1
                self.contract_number = "TEST-QR-001"
                self.title = "عقد اختبار QR"
                self.first_party_name = "الطرف الأول"
                self.second_party_name = "الطرف الثاني"
                self.contract_date = datetime.now().date()
                self.contract_value = 5000
                self.currency = "QAR"
                self.status = "active"
                self.created_at = datetime.now()
        
        contract = MockContract()
        qr_service = QRService()
        
        print("   📝 Creating QR code...")
        result = qr_service.generate_contract_qr(contract, include_verification=True)
        
        if result['success']:
            print(f"   ✅ QR Code created successfully!")
            print(f"   📊 Base64 length: {len(result['base64'])}")
            print(f"   🔐 Verification hash: {result.get('verification_hash', 'N/A')}")
            return True
        else:
            print(f"   ❌ QR Code creation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ QR Service error: {str(e)}")
        traceback.print_exc()
        return False

def test_bilingual_service():
    """Test Bilingual service directly"""
    print("\n🌐 Testing Bilingual Service...")
    try:
        from utils.bilingual_service import BilingualService
        
        # Create mock contract
        class MockContractType:
            def __init__(self):
                self.name = "بيع سيارة"
        
        class MockContract:
            def __init__(self):
                self.id = 1
                self.contract_number = "TEST-BIL-001"
                self.title = "عقد اختبار ثنائي اللغة"
                self.first_party_name = "أحمد محمد"
                self.second_party_name = "شركة قطر للسيارات"
                self.contract_date = datetime.now().date()
                self.contract_value = 150000
                self.currency = "QAR"
                self.content = "هذا عقد بيع سيارة بين الطرفين المذكورين أعلاه."
                self.terms_conditions = "يلتزم الطرف الأول بدفع المبلغ كاملاً."
                self.contract_type = MockContractType()
        
        contract = MockContract()
        bilingual_service = BilingualService()
        
        print("   📝 Creating bilingual contract...")
        result = bilingual_service.create_bilingual_contract(contract, include_english=True)
        
        if result['success']:
            print(f"   ✅ Bilingual contract created successfully!")
            print(f"   📊 Content length: {len(result['content'])}")
            print(f"   🇦🇪 Arabic content: {len(result['arabic_content'])} chars")
            print(f"   🇬🇧 English content: {len(result['english_content'])} chars")
            return True
        else:
            print(f"   ❌ Bilingual contract creation failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ Bilingual Service error: {str(e)}")
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test API endpoints with real Flask app"""
    print("\n🌐 Testing API Endpoints...")
    try:
        from app import create_app
        from models.contract import Contract
        
        app = create_app()
        
        with app.app_context():
            # Get first contract
            contract = Contract.query.first()
            if not contract:
                print("   ❌ No contracts found in database")
                return False
            
            print(f"   📋 Using contract: {contract.contract_number}")
            
            # Test QR generation
            print("   🔍 Testing QR API...")
            try:
                from utils.qr_service import QRService
                qr_service = QRService()
                qr_result = qr_service.generate_contract_qr(contract, include_verification=True)
                if qr_result['success']:
                    print("   ✅ QR API works!")
                else:
                    print(f"   ❌ QR API failed: {qr_result['message']}")
            except Exception as e:
                print(f"   ❌ QR API error: {str(e)}")
            
            # Test Bilingual generation
            print("   🌐 Testing Bilingual API...")
            try:
                from utils.bilingual_service import BilingualService
                bilingual_service = BilingualService()
                bilingual_result = bilingual_service.create_bilingual_contract(contract, include_english=True)
                if bilingual_result['success']:
                    print("   ✅ Bilingual API works!")
                else:
                    print(f"   ❌ Bilingual API failed: {bilingual_result['message']}")
            except Exception as e:
                print(f"   ❌ Bilingual API error: {str(e)}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ API test error: {str(e)}")
        traceback.print_exc()
        return False

def check_imports():
    """Check all required imports"""
    print("📦 Checking imports...")
    
    imports_to_check = [
        ('qrcode', 'QR Code library'),
        ('PIL', 'Pillow (PIL) library'),
        ('requests', 'Requests library'),
        ('schedule', 'Schedule library'),
    ]
    
    all_good = True
    
    for module, description in imports_to_check:
        try:
            __import__(module)
            print(f"   ✅ {description}: OK")
        except ImportError:
            print(f"   ❌ {description}: MISSING")
            all_good = False
    
    return all_good

def main():
    """Main debug function"""
    print("🐛 Debug Services - Advanced Features")
    print("=" * 50)
    
    # Check imports first
    imports_ok = check_imports()
    if not imports_ok:
        print("\n⚠️ Some required libraries are missing!")
        print("Run: pip install qrcode[pil] requests schedule")
        return
    
    # Test services
    qr_ok = test_qr_service()
    bilingual_ok = test_bilingual_service()
    api_ok = test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"   QR Service: {'✅ OK' if qr_ok else '❌ FAILED'}")
    print(f"   Bilingual Service: {'✅ OK' if bilingual_ok else '❌ FAILED'}")
    print(f"   API Endpoints: {'✅ OK' if api_ok else '❌ FAILED'}")
    
    if qr_ok and bilingual_ok and api_ok:
        print("\n🎉 All services are working correctly!")
        print("The web interface should work now.")
    else:
        print("\n⚠️ Some services have issues.")
        print("Check the error messages above for details.")

if __name__ == '__main__':
    main()
