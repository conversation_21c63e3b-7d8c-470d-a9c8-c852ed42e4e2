from datetime import datetime
from . import db

class EmailSettings(db.Model):
    __tablename__ = 'email_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    smtp_server = db.Column(db.String(255), nullable=False)
    smtp_port = db.Column(db.Integer, nullable=False, default=587)
    smtp_username = db.Column(db.String(255), nullable=False)
    smtp_password = db.Column(db.String(255), nullable=False)
    use_tls = db.Column(db.<PERSON><PERSON>an, default=True)
    from_email = db.Column(db.String(255), nullable=False)
    from_name = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, smtp_server, smtp_port, smtp_username, smtp_password, 
                 from_email, from_name, use_tls=True):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_username = smtp_username
        self.smtp_password = smtp_password
        self.from_email = from_email
        self.from_name = from_name
        self.use_tls = use_tls
    
    @staticmethod
    def get_active_settings():
        """Get active email settings"""
        return EmailSettings.query.filter_by(is_active=True).first()
    
    def to_dict(self):
        return {
            'id': self.id,
            'smtp_server': self.smtp_server,
            'smtp_port': self.smtp_port,
            'smtp_username': self.smtp_username,
            'from_email': self.from_email,
            'from_name': self.from_name,
            'use_tls': self.use_tls,
            'is_active': self.is_active
        }

class WhatsAppSettings(db.Model):
    __tablename__ = 'whatsapp_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    api_url = db.Column(db.String(500), nullable=False)
    api_token = db.Column(db.String(500), nullable=False)
    phone_number_id = db.Column(db.String(100), nullable=False)
    business_account_id = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, api_url, api_token, phone_number_id, business_account_id):
        self.api_url = api_url
        self.api_token = api_token
        self.phone_number_id = phone_number_id
        self.business_account_id = business_account_id
    
    @staticmethod
    def get_active_settings():
        """Get active WhatsApp settings"""
        return WhatsAppSettings.query.filter_by(is_active=True).first()
    
    def to_dict(self):
        return {
            'id': self.id,
            'api_url': self.api_url,
            'phone_number_id': self.phone_number_id,
            'business_account_id': self.business_account_id,
            'is_active': self.is_active
        }

class ContractSendLog(db.Model):
    __tablename__ = 'contract_send_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=False)
    send_method = db.Column(db.String(50), nullable=False)  # email, whatsapp
    recipient = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(50), nullable=False)  # sent, failed, pending
    message = db.Column(db.Text)
    sent_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    contract = db.relationship('Contract', backref='send_logs')
    user = db.relationship('User', backref='sent_contracts')
    
    def __init__(self, contract_id, send_method, recipient, status, sent_by, message=None):
        self.contract_id = contract_id
        self.send_method = send_method
        self.recipient = recipient
        self.status = status
        self.sent_by = sent_by
        self.message = message
    
    def to_dict(self):
        return {
            'id': self.id,
            'contract_id': self.contract_id,
            'send_method': self.send_method,
            'recipient': self.recipient,
            'status': self.status,
            'message': self.message,
            'sent_by': self.sent_by,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None
        }
