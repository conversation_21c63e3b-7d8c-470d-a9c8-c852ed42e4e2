import requests
import json
import os
from datetime import datetime
from models.email_settings import WhatsAppSettings, ContractSendLog
from models import db

class WhatsAppService:
    def __init__(self):
        self.settings = WhatsAppSettings.get_active_settings()
    
    def send_contract_whatsapp(self, contract, recipient_phone, recipient_name, sent_by_user_id, 
                              include_pdf=True, custom_message=None):
        """Send contract via WhatsApp"""
        
        if not self.settings:
            return {
                'success': False,
                'message': 'إعدادات WhatsApp غير مكونة'
            }
        
        try:
            # Clean phone number
            phone_number = self._clean_phone_number(recipient_phone)
            
            # Send text message first
            text_result = self._send_text_message(contract, phone_number, recipient_name, custom_message)
            
            if not text_result['success']:
                return text_result
            
            # Send document if requested
            if include_pdf:
                pdf_path = self._generate_contract_pdf(contract)
                if pdf_path and os.path.exists(pdf_path):
                    doc_result = self._send_document(phone_number, pdf_path, 
                                                   f"عقد_{contract.contract_number}.pdf")
                    
                    if not doc_result['success']:
                        # Log partial success (text sent, document failed)
                        self._log_send_attempt(contract.id, 'whatsapp', recipient_phone, 'partial', 
                                             sent_by_user_id, 
                                             f'تم إرسال النص بنجاح، فشل في إرسال المرفق: {doc_result["message"]}')
                        return doc_result
            
            # Log success
            self._log_send_attempt(contract.id, 'whatsapp', recipient_phone, 'sent', 
                                 sent_by_user_id, 'تم إرسال الرسالة والمرفق بنجاح')
            
            return {
                'success': True,
                'message': f'تم إرسال العقد بنجاح عبر WhatsApp إلى {recipient_phone}'
            }
            
        except Exception as e:
            error_message = f'فشل في إرسال رسالة WhatsApp: {str(e)}'
            
            # Log failure
            self._log_send_attempt(contract.id, 'whatsapp', recipient_phone, 'failed', 
                                 sent_by_user_id, error_message)
            
            return {
                'success': False,
                'message': error_message
            }
    
    def _send_text_message(self, contract, phone_number, recipient_name, custom_message=None):
        """Send text message via WhatsApp API"""
        
        message_text = self._create_whatsapp_message(contract, recipient_name, custom_message)
        
        url = f"{self.settings.api_url}/{self.settings.phone_number_id}/messages"
        
        headers = {
            'Authorization': f'Bearer {self.settings.api_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": phone_number,
            "type": "text",
            "text": {
                "body": message_text
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            return {
                'success': True,
                'message': 'تم إرسال النص بنجاح'
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'فشل في إرسال النص: {str(e)}'
            }
    
    def _send_document(self, phone_number, file_path, filename):
        """Send document via WhatsApp API"""
        
        # First, upload the document
        upload_result = self._upload_media(file_path)
        
        if not upload_result['success']:
            return upload_result
        
        media_id = upload_result['media_id']
        
        # Send document message
        url = f"{self.settings.api_url}/{self.settings.phone_number_id}/messages"
        
        headers = {
            'Authorization': f'Bearer {self.settings.api_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": phone_number,
            "type": "document",
            "document": {
                "id": media_id,
                "filename": filename,
                "caption": f"العقد المرفق: {filename}"
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            return {
                'success': True,
                'message': 'تم إرسال المرفق بنجاح'
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'فشل في إرسال المرفق: {str(e)}'
            }
    
    def _upload_media(self, file_path):
        """Upload media to WhatsApp"""
        
        url = f"{self.settings.api_url}/{self.settings.phone_number_id}/media"
        
        headers = {
            'Authorization': f'Bearer {self.settings.api_token}'
        }
        
        try:
            with open(file_path, 'rb') as file:
                files = {
                    'file': (os.path.basename(file_path), file, 'application/pdf'),
                    'type': (None, 'application/pdf'),
                    'messaging_product': (None, 'whatsapp')
                }
                
                response = requests.post(url, headers=headers, files=files, timeout=60)
                response.raise_for_status()
                
                result = response.json()
                
                if 'id' in result:
                    return {
                        'success': True,
                        'media_id': result['id']
                    }
                else:
                    return {
                        'success': False,
                        'message': 'فشل في رفع الملف'
                    }
                    
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'فشل في رفع الملف: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في رفع الملف: {str(e)}'
            }
    
    def _create_whatsapp_message(self, contract, recipient_name, custom_message=None):
        """Create WhatsApp message text"""
        
        message = f"""🏛️ *نظام إدارة العقود الإلكترونية - دولة قطر*

السلام عليكم ورحمة الله وبركاته

عزيزي/عزيزتي *{recipient_name}*،

{custom_message if custom_message else 'نرسل لكم العقد المرفق للمراجعة والتوقيع.'}

📋 *تفاصيل العقد:*
• رقم العقد: {contract.contract_number}
• عنوان العقد: {contract.title}
• تاريخ العقد: {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else 'غير محدد'}
• الطرف الأول: {contract.first_party_name}
• الطرف الثاني: {contract.second_party_name}"""

        if contract.contract_value:
            message += f"\n• قيمة العقد: {contract.contract_value} {contract.currency}"

        message += f"""

يرجى مراجعة العقد المرفق والتواصل معنا في حالة وجود أي استفسارات.

📅 تاريخ الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M')}

© 2024 نظام إدارة العقود الإلكترونية"""

        return message
    
    def _clean_phone_number(self, phone):
        """Clean and format phone number"""
        # Remove all non-digit characters
        phone = ''.join(filter(str.isdigit, phone))
        
        # Add country code if not present (Qatar +974)
        if len(phone) == 8 and phone.startswith(('3', '4', '5', '6', '7')):
            phone = '974' + phone
        elif len(phone) == 11 and phone.startswith('974'):
            pass  # Already has country code
        elif phone.startswith('00974'):
            phone = phone[2:]  # Remove 00 prefix
        elif phone.startswith('+974'):
            phone = phone[1:]  # Remove + prefix
        
        return phone
    
    def _generate_contract_pdf(self, contract):
        """Generate PDF for contract"""
        try:
            from utils.pdf_generator import generate_pdf
            return generate_pdf(contract)
        except Exception as e:
            print(f"Error generating PDF: {e}")
            return None
    
    def _log_send_attempt(self, contract_id, method, recipient, status, sent_by, message):
        """Log send attempt"""
        try:
            log = ContractSendLog(
                contract_id=contract_id,
                send_method=method,
                recipient=recipient,
                status=status,
                sent_by=sent_by,
                message=message
            )
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            print(f"Error logging send attempt: {e}")
            db.session.rollback()
    
    def test_connection(self):
        """Test WhatsApp API connection"""
        if not self.settings:
            return {
                'success': False,
                'message': 'إعدادات WhatsApp غير مكونة'
            }
        
        try:
            # Test by getting phone number info
            url = f"{self.settings.api_url}/{self.settings.phone_number_id}"
            
            headers = {
                'Authorization': f'Bearer {self.settings.api_token}'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            return {
                'success': True,
                'message': 'تم الاتصال بـ WhatsApp API بنجاح'
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'فشل في الاتصال بـ WhatsApp API: {str(e)}'
            }
