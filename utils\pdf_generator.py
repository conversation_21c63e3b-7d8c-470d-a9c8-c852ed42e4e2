import os
import tempfile
from datetime import datetime
# from weasyprint import HTML, CSS
# from weasyprint.text.fonts import FontConfiguration

def generate_pdf(contract):
    """Generate PDF from contract data"""
    
    # Create HTML content
    html_content = create_contract_html(contract)
    
    # Create CSS for Arabic support and styling
    css_content = """
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
    
    body {
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        margin: 40px;
        color: #333;
    }
    
    .header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 20px;
    }
    
    .contract-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .contract-number {
        font-size: 14px;
        color: #666;
    }
    
    .parties {
        margin: 30px 0;
    }
    
    .party {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f9f9f9;
        border-right: 4px solid #007bff;
    }
    
    .party-title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    .content {
        margin: 30px 0;
        line-height: 1.8;
    }
    
    .terms {
        margin-top: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }
    
    .signatures {
        margin-top: 50px;
        display: flex;
        justify-content: space-between;
    }
    
    .signature-box {
        width: 200px;
        height: 100px;
        border: 1px solid #333;
        text-align: center;
        padding: 10px;
    }
    
    .footer {
        margin-top: 50px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 20px;
    }
    
    @page {
        size: A4;
        margin: 2cm;
    }
    """
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as html_file:
        html_file.write(html_content)
        html_file_path = html_file.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.css', delete=False, encoding='utf-8') as css_file:
        css_file.write(css_content)
        css_file_path = css_file.name
    
    # Generate PDF (simplified version without WeasyPrint)
    try:
        # Create output directory
        output_dir = os.path.join('static', 'exports', 'pdf')
        os.makedirs(output_dir, exist_ok=True)

        # For now, just save as HTML file (can be converted to PDF later)
        pdf_filename = f"{contract.contract_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        pdf_path = os.path.join(output_dir, pdf_filename)

        # Copy HTML content to output file
        with open(pdf_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return pdf_path
        
    finally:
        # Clean up temporary files
        try:
            os.unlink(html_file_path)
            os.unlink(css_file_path)
        except:
            pass

def create_contract_html(contract):
    """Create HTML content for contract"""
    
    # Process content with variables
    content = contract.content
    
    # Replace variables
    variables = {
        'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '',
        'first_party_name': contract.first_party_name,
        'second_party_name': contract.second_party_name,
        'contract_number': contract.contract_number,
        'contract_value': str(contract.contract_value) if contract.contract_value else '',
        'currency': contract.currency
    }
    
    # Add custom fields
    if contract.custom_fields:
        variables.update(contract.custom_fields)
    
    # Replace variables in content
    for key, value in variables.items():
        content = content.replace(f'{{{{{key}}}}}', str(value))
    
    html_template = f"""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{contract.title}</title>
    </head>
    <body>
        <div class="header">
            <div class="contract-title">{contract.title}</div>
            <div class="contract-number">رقم العقد: {contract.contract_number}</div>
            <div class="contract-date">تاريخ العقد: {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else ''}</div>
        </div>
        
        <div class="parties">
            <div class="party">
                <div class="party-title">الطرف الأول:</div>
                <div>الاسم: {contract.first_party_name}</div>
                {f'<div>رقم الهوية: {contract.first_party_id}</div>' if contract.first_party_id else ''}
                {f'<div>العنوان: {contract.first_party_address}</div>' if contract.first_party_address else ''}
                {f'<div>الهاتف: {contract.first_party_phone}</div>' if contract.first_party_phone else ''}
            </div>
            
            <div class="party">
                <div class="party-title">الطرف الثاني:</div>
                <div>الاسم: {contract.second_party_name}</div>
                {f'<div>رقم الهوية: {contract.second_party_id}</div>' if contract.second_party_id else ''}
                {f'<div>العنوان: {contract.second_party_address}</div>' if contract.second_party_address else ''}
                {f'<div>الهاتف: {contract.second_party_phone}</div>' if contract.second_party_phone else ''}
            </div>
        </div>
        
        <div class="content">
            {content}
        </div>
        
        {f'<div class="terms"><h3>الشروط والأحكام:</h3>{contract.terms_conditions}</div>' if contract.terms_conditions else ''}
        
        <div class="signatures">
            <div class="signature-box">
                <div>توقيع الطرف الأول</div>
                <div style="margin-top: 20px;">_________________</div>
            </div>
            <div class="signature-box">
                <div>توقيع الطرف الثاني</div>
                <div style="margin-top: 20px;">_________________</div>
            </div>
            <div class="signature-box">
                <div>ختم الشركة</div>
                <div style="margin-top: 20px;">_________________</div>
            </div>
        </div>
        
        <div class="footer">
            <div>تم إنشاء هذا العقد بواسطة نظام إدارة العقود الإلكترونية</div>
            <div>تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
    </body>
    </html>
    """
    
    return html_template
