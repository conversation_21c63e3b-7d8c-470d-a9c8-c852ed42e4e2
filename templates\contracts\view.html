{% extends "base.html" %}

{% block title %}{{ contract.title }} - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-contract me-2"></i>
                    {{ contract.title }}
                </h1>
                <div>
                    {% if contract.can_edit(current_user) %}
                    <a href="{{ url_for('contracts.edit', id=contract.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </a>
                    {% endif %}
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='pdf') }}">
                                <i class="fas fa-file-pdf me-1"></i> PDF
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='word') }}">
                                <i class="fas fa-file-word me-1"></i> Word
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="generateBilingualContract()">
                                <i class="fas fa-language me-1"></i> ثنائي اللغة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="generateQRCode()">
                                <i class="fas fa-qrcode me-1"></i> مع QR Code
                            </a></li>
                        </ul>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-share me-1"></i>
                            إرسال
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="openEmailModal()">
                                <i class="fas fa-envelope me-1"></i> بريد إلكتروني
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="openWhatsAppModal()">
                                <i class="fab fa-whatsapp me-1"></i> WhatsApp
                            </a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" onclick="printContract()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    <a href="{{ url_for('contracts.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contract Information -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Contract Content -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">محتوى العقد</h6>
                </div>
                <div class="card-body contract-preview" id="contract-content">
                    <div class="text-center mb-4">
                        <h2>{{ contract.title }}</h2>
                        <p class="text-muted">رقم العقد: {{ contract.contract_number }}</p>
                        <p class="text-muted">تاريخ العقد: {{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '' }}</p>
                    </div>
                    
                    <div class="parties-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="party">
                                    <h5 class="party-title">الطرف الأول:</h5>
                                    <p><strong>الاسم:</strong> {{ contract.first_party_name }}</p>
                                    {% if contract.first_party_id %}
                                    <p><strong>رقم الهوية:</strong> {{ contract.first_party_id }}</p>
                                    {% endif %}
                                    {% if contract.first_party_phone %}
                                    <p><strong>الهاتف:</strong> {{ contract.first_party_phone }}</p>
                                    {% endif %}
                                    {% if contract.first_party_address %}
                                    <p><strong>العنوان:</strong> {{ contract.first_party_address }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="party">
                                    <h5 class="party-title">الطرف الثاني:</h5>
                                    <p><strong>الاسم:</strong> {{ contract.second_party_name }}</p>
                                    {% if contract.second_party_id %}
                                    <p><strong>رقم الهوية:</strong> {{ contract.second_party_id }}</p>
                                    {% endif %}
                                    {% if contract.second_party_phone %}
                                    <p><strong>الهاتف:</strong> {{ contract.second_party_phone }}</p>
                                    {% endif %}
                                    {% if contract.second_party_address %}
                                    <p><strong>العنوان:</strong> {{ contract.second_party_address }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contract-content mb-4">
                        {{ contract.content|safe }}
                    </div>
                    
                    {% if contract.contract_value %}
                    <div class="contract-value mb-4">
                        <h5>قيمة العقد:</h5>
                        <p class="h4 text-primary">{{ contract.contract_value|currency(contract.currency) }}</p>
                    </div>
                    {% endif %}
                    
                    {% if contract.terms_conditions %}
                    <div class="terms-conditions mb-4">
                        <h5>الشروط والأحكام:</h5>
                        <div class="bg-light p-3 rounded">
                            {{ contract.terms_conditions|nl2br|safe }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Signatures Section -->
                    <div class="signature-section mt-5">
                        <h5 class="mb-4">التوقيعات:</h5>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>توقيع الطرف الأول</h6>
                                    {% if contract.first_party_signature %}
                                        <img src="data:image/png;base64,{{ contract.first_party_signature }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير موقع</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('first_party')">
                                        <i class="fas fa-signature me-1"></i>
                                        توقيع
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>توقيع الطرف الثاني</h6>
                                    {% if contract.second_party_signature %}
                                        <img src="data:image/png;base64,{{ contract.second_party_signature }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير موقع</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('second_party')">
                                        <i class="fas fa-signature me-1"></i>
                                        توقيع
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>ختم الشركة</h6>
                                    {% if contract.company_seal %}
                                        <img src="data:image/png;base64,{{ contract.company_seal }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير مختوم</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('company_seal')">
                                        <i class="fas fa-stamp me-1"></i>
                                        ختم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Contract Details -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل العقد</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>رقم العقد:</strong></td>
                            <td>{{ contract.contract_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>النوع:</strong></td>
                            <td>{{ contract.contract_type.name if contract.contract_type else '' }}</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <span class="badge bg-{{ 'success' if contract.status == 'active' else 'warning' if contract.status == 'draft' else 'secondary' }}">
                                    {{ contract.get_status_display() }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ contract.created_at.strftime('%Y-%m-%d') if contract.created_at else '' }}</td>
                        </tr>
                        {% if contract.start_date %}
                        <tr>
                            <td><strong>تاريخ البداية:</strong></td>
                            <td>{{ contract.start_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endif %}
                        {% if contract.end_date %}
                        <tr>
                            <td><strong>تاريخ النهاية:</strong></td>
                            <td>{{ contract.end_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><strong>منشئ العقد:</strong></td>
                            <td>{{ contract.created_by_user.full_name if contract.created_by_user else '' }}</td>
                        </tr>
                        <tr>
                            <td><strong>موقع:</strong></td>
                            <td>
                                {% if contract.is_signed() %}
                                    <span class="text-success"><i class="fas fa-check-circle"></i> موقع</span>
                                {% else %}
                                    <span class="text-warning"><i class="fas fa-clock"></i> في انتظار التوقيع</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Status Update -->
            {% if contract.can_edit(current_user) %}
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">تحديث الحالة</h6>
                </div>
                <div class="card-body">
                    <select class="form-select status-select" data-contract-id="{{ contract.id }}">
                        <option value="draft" {{ 'selected' if contract.status == 'draft' else '' }}>مسودة</option>
                        <option value="active" {{ 'selected' if contract.status == 'active' else '' }}>نشط</option>
                        <option value="completed" {{ 'selected' if contract.status == 'completed' else '' }}>مكتمل</option>
                        <option value="cancelled" {{ 'selected' if contract.status == 'cancelled' else '' }}>ملغي</option>
                    </select>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Signature Modal -->
<div class="modal fade" id="signatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalTitle">التوقيع الإلكتروني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <canvas id="signature-pad" class="signature-pad" width="460" height="200"></canvas>
                <div class="signature-controls">
                    <button type="button" class="btn btn-secondary" onclick="clearSignature()">
                        <i class="fas fa-eraser me-1"></i>
                        مسح
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveSignature()">
                        <i class="fas fa-save me-1"></i>
                        حفظ التوقيع
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Modal -->
<div class="modal fade" id="emailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال العقد عبر البريد الإلكتروني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="mb-3">
                        <label for="recipientEmail" class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-control" id="recipientEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="recipientName" class="form-label">اسم المستلم *</label>
                        <input type="text" class="form-control" id="recipientName" required>
                    </div>
                    <div class="mb-3">
                        <label for="emailMessage" class="form-label">رسالة مخصصة</label>
                        <textarea class="form-control" id="emailMessage" rows="3"
                                  placeholder="رسالة اختيارية مع العقد..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includePdf" checked>
                            <label class="form-check-label" for="includePdf">
                                إرفاق ملف PDF
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeWord">
                            <label class="form-check-label" for="includeWord">
                                إرفاق ملف Word
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="sendEmail()">
                    <i class="fas fa-paper-plane me-1"></i>
                    إرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- WhatsApp Modal -->
<div class="modal fade" id="whatsappModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال العقد عبر WhatsApp</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="whatsappForm">
                    <div class="mb-3">
                        <label for="recipientPhone" class="form-label">رقم الهاتف *</label>
                        <input type="tel" class="form-control" id="recipientPhone"
                               placeholder="مثال: +97450123456 أو 50123456" required>
                        <div class="form-text">أدخل رقم الهاتف مع رمز الدولة أو بدونه</div>
                    </div>
                    <div class="mb-3">
                        <label for="whatsappName" class="form-label">اسم المستلم *</label>
                        <input type="text" class="form-control" id="whatsappName" required>
                    </div>
                    <div class="mb-3">
                        <label for="whatsappMessage" class="form-label">رسالة مخصصة</label>
                        <textarea class="form-control" id="whatsappMessage" rows="3"
                                  placeholder="رسالة اختيارية مع العقد..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeWhatsappPdf" checked>
                            <label class="form-check-label" for="includeWhatsappPdf">
                                إرفاق ملف PDF
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="sendWhatsApp()">
                    <i class="fab fa-whatsapp me-1"></i>
                    إرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">QR Code للعقد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="qrCodeContainer">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري الإنشاء...</span>
                    </div>
                    <p class="mt-2">جاري إنشاء QR Code...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="downloadQR" style="display: none;">
                    <i class="fas fa-download me-1"></i>
                    تحميل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/signature.js') }}"></script>
<script>
    // Initialize signature functionality
    var currentSignatureType = '';
    var contractId = {{ contract.id }};

    // Email Modal Functions
    function openEmailModal() {
        // Pre-fill with contract party information
        document.getElementById('recipientName').value = '{{ contract.first_party_name }}';
        document.getElementById('recipientEmail').value = ''; // Could be pre-filled if available

        var emailModal = new bootstrap.Modal(document.getElementById('emailModal'));
        emailModal.show();
    }

    function sendEmail() {
        const form = document.getElementById('emailForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const data = {
            email: document.getElementById('recipientEmail').value,
            name: document.getElementById('recipientName').value,
            message: document.getElementById('emailMessage').value,
            include_pdf: document.getElementById('includePdf').checked,
            include_word: document.getElementById('includeWord').checked
        };

        // Show loading
        const sendBtn = event.target;
        const originalText = sendBtn.innerHTML;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الإرسال...';
        sendBtn.disabled = true;

        fetch(`/api/contracts/${contractId}/send-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('تم إرسال العقد بنجاح عبر البريد الإلكتروني', 'success');
                bootstrap.Modal.getInstance(document.getElementById('emailModal')).hide();
            } else {
                showAlert(result.message || 'فشل في إرسال البريد الإلكتروني', 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ أثناء إرسال البريد الإلكتروني', 'error');
        })
        .finally(() => {
            sendBtn.innerHTML = originalText;
            sendBtn.disabled = false;
        });
    }

    // WhatsApp Modal Functions
    function openWhatsAppModal() {
        // Pre-fill with contract party information
        document.getElementById('whatsappName').value = '{{ contract.first_party_name }}';
        document.getElementById('recipientPhone').value = ''; // Could be pre-filled if available

        var whatsappModal = new bootstrap.Modal(document.getElementById('whatsappModal'));
        whatsappModal.show();
    }

    function sendWhatsApp() {
        const form = document.getElementById('whatsappForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const data = {
            phone: document.getElementById('recipientPhone').value,
            name: document.getElementById('whatsappName').value,
            message: document.getElementById('whatsappMessage').value,
            include_pdf: document.getElementById('includeWhatsappPdf').checked
        };

        // Show loading
        const sendBtn = event.target;
        const originalText = sendBtn.innerHTML;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الإرسال...';
        sendBtn.disabled = true;

        fetch(`/api/contracts/${contractId}/send-whatsapp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('تم إرسال العقد بنجاح عبر WhatsApp', 'success');
                bootstrap.Modal.getInstance(document.getElementById('whatsappModal')).hide();
            } else {
                showAlert(result.message || 'فشل في إرسال رسالة WhatsApp', 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ أثناء إرسال رسالة WhatsApp', 'error');
        })
        .finally(() => {
            sendBtn.innerHTML = originalText;
            sendBtn.disabled = false;
        });
    }

    // QR Code Functions
    function generateQRCode() {
        var qrModal = new bootstrap.Modal(document.getElementById('qrModal'));
        qrModal.show();

        // Reset container
        document.getElementById('qrCodeContainer').innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري الإنشاء...</span>
            </div>
            <p class="mt-2">جاري إنشاء QR Code...</p>
        `;
        document.getElementById('downloadQR').style.display = 'none';

        fetch(`/api/contracts/${contractId}/generate-qr`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ include_verification: true })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                document.getElementById('qrCodeContainer').innerHTML = `
                    <div class="qr-code-display">
                        <img src="data:image/png;base64,${result.base64}"
                             alt="QR Code للعقد" class="img-fluid mb-3" style="max-width: 300px;">
                        <h6>رقم العقد: {{ contract.contract_number }}</h6>
                        <p class="text-muted">امسح الرمز للتحقق من صحة العقد</p>
                        ${result.verification_hash ? `<p class="small text-muted">رمز التحقق: ${result.verification_hash}</p>` : ''}
                    </div>
                `;
                document.getElementById('downloadQR').style.display = 'inline-block';

                // Store QR data for download
                window.currentQRData = result.base64;
            } else {
                document.getElementById('qrCodeContainer').innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>فشل في إنشاء QR Code</p>
                        <p class="small">${result.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('qrCodeContainer').innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <p>حدث خطأ أثناء إنشاء QR Code</p>
                </div>
            `;
        });
    }

    // Download QR Code
    document.getElementById('downloadQR').addEventListener('click', function() {
        if (window.currentQRData) {
            const link = document.createElement('a');
            link.download = `contract_${contractId}_qr.png`;
            link.href = `data:image/png;base64,${window.currentQRData}`;
            link.click();
        }
    });

    // Bilingual Contract
    function generateBilingualContract() {
        // Show loading
        showAlert('جاري إنشاء العقد ثنائي اللغة...', 'info');

        fetch(`/api/contracts/${contractId}/bilingual`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ include_english: true })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // Open in new window
                const newWindow = window.open('', '_blank');
                newWindow.document.write(`
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>عقد ثنائي اللغة - {{ contract.contract_number }}</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                            .arabic-version { direction: rtl; text-align: right; }
                            .english-version { direction: ltr; text-align: left; }
                            @media print {
                                .no-print { display: none !important; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container my-4">
                            <div class="no-print mb-3">
                                <button onclick="window.print()" class="btn btn-primary">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                                <button onclick="window.close()" class="btn btn-secondary">
                                    إغلاق
                                </button>
                            </div>
                            ${result.content}
                        </div>
                    </body>
                    </html>
                `);
                newWindow.document.close();

                showAlert('تم إنشاء العقد ثنائي اللغة بنجاح', 'success');
            } else {
                showAlert(result.message || 'فشل في إنشاء العقد ثنائي اللغة', 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ أثناء إنشاء العقد ثنائي اللغة', 'error');
        });
    }

    // Print contract
    function printContract() {
        window.print();
    }
</script>
{% endblock %}
