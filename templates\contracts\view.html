{% extends "base.html" %}

{% block title %}{{ contract.title }} - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-contract me-2"></i>
                    {{ contract.title }}
                </h1>
                <div>
                    {% if contract.can_edit(current_user) %}
                    <a href="{{ url_for('contracts.edit', id=contract.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </a>
                    {% endif %}
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='pdf') }}">
                                <i class="fas fa-file-pdf me-1"></i> PDF
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('contracts.export', id=contract.id, format='word') }}">
                                <i class="fas fa-file-word me-1"></i> Word
                            </a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" onclick="printContract()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    <a href="{{ url_for('contracts.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contract Information -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Contract Content -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">محتوى العقد</h6>
                </div>
                <div class="card-body contract-preview" id="contract-content">
                    <div class="text-center mb-4">
                        <h2>{{ contract.title }}</h2>
                        <p class="text-muted">رقم العقد: {{ contract.contract_number }}</p>
                        <p class="text-muted">تاريخ العقد: {{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '' }}</p>
                    </div>
                    
                    <div class="parties-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="party">
                                    <h5 class="party-title">الطرف الأول:</h5>
                                    <p><strong>الاسم:</strong> {{ contract.first_party_name }}</p>
                                    {% if contract.first_party_id %}
                                    <p><strong>رقم الهوية:</strong> {{ contract.first_party_id }}</p>
                                    {% endif %}
                                    {% if contract.first_party_phone %}
                                    <p><strong>الهاتف:</strong> {{ contract.first_party_phone }}</p>
                                    {% endif %}
                                    {% if contract.first_party_address %}
                                    <p><strong>العنوان:</strong> {{ contract.first_party_address }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="party">
                                    <h5 class="party-title">الطرف الثاني:</h5>
                                    <p><strong>الاسم:</strong> {{ contract.second_party_name }}</p>
                                    {% if contract.second_party_id %}
                                    <p><strong>رقم الهوية:</strong> {{ contract.second_party_id }}</p>
                                    {% endif %}
                                    {% if contract.second_party_phone %}
                                    <p><strong>الهاتف:</strong> {{ contract.second_party_phone }}</p>
                                    {% endif %}
                                    {% if contract.second_party_address %}
                                    <p><strong>العنوان:</strong> {{ contract.second_party_address }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contract-content mb-4">
                        {{ contract.content|safe }}
                    </div>
                    
                    {% if contract.contract_value %}
                    <div class="contract-value mb-4">
                        <h5>قيمة العقد:</h5>
                        <p class="h4 text-primary">{{ contract.contract_value }} {{ contract.currency }}</p>
                    </div>
                    {% endif %}
                    
                    {% if contract.terms_conditions %}
                    <div class="terms-conditions mb-4">
                        <h5>الشروط والأحكام:</h5>
                        <div class="bg-light p-3 rounded">
                            {{ contract.terms_conditions|nl2br|safe }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Signatures Section -->
                    <div class="signature-section mt-5">
                        <h5 class="mb-4">التوقيعات:</h5>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>توقيع الطرف الأول</h6>
                                    {% if contract.first_party_signature %}
                                        <img src="data:image/png;base64,{{ contract.first_party_signature }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير موقع</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('first_party')">
                                        <i class="fas fa-signature me-1"></i>
                                        توقيع
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>توقيع الطرف الثاني</h6>
                                    {% if contract.second_party_signature %}
                                        <img src="data:image/png;base64,{{ contract.second_party_signature }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير موقع</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('second_party')">
                                        <i class="fas fa-signature me-1"></i>
                                        توقيع
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box p-3 border rounded">
                                    <h6>ختم الشركة</h6>
                                    {% if contract.company_seal %}
                                        <img src="data:image/png;base64,{{ contract.company_seal }}" 
                                             class="img-fluid" style="max-height: 100px;">
                                    {% else %}
                                        <div class="signature-placeholder" style="height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                            <span class="text-muted">غير مختوم</span>
                                        </div>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="openSignatureModal('company_seal')">
                                        <i class="fas fa-stamp me-1"></i>
                                        ختم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Contract Details -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل العقد</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>رقم العقد:</strong></td>
                            <td>{{ contract.contract_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>النوع:</strong></td>
                            <td>{{ contract.contract_type.name if contract.contract_type else '' }}</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <span class="badge bg-{{ 'success' if contract.status == 'active' else 'warning' if contract.status == 'draft' else 'secondary' }}">
                                    {{ contract.get_status_display() }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ contract.created_at.strftime('%Y-%m-%d') if contract.created_at else '' }}</td>
                        </tr>
                        {% if contract.start_date %}
                        <tr>
                            <td><strong>تاريخ البداية:</strong></td>
                            <td>{{ contract.start_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endif %}
                        {% if contract.end_date %}
                        <tr>
                            <td><strong>تاريخ النهاية:</strong></td>
                            <td>{{ contract.end_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><strong>منشئ العقد:</strong></td>
                            <td>{{ contract.created_by_user.full_name if contract.created_by_user else '' }}</td>
                        </tr>
                        <tr>
                            <td><strong>موقع:</strong></td>
                            <td>
                                {% if contract.is_signed() %}
                                    <span class="text-success"><i class="fas fa-check-circle"></i> موقع</span>
                                {% else %}
                                    <span class="text-warning"><i class="fas fa-clock"></i> في انتظار التوقيع</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Status Update -->
            {% if contract.can_edit(current_user) %}
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">تحديث الحالة</h6>
                </div>
                <div class="card-body">
                    <select class="form-select status-select" data-contract-id="{{ contract.id }}">
                        <option value="draft" {{ 'selected' if contract.status == 'draft' else '' }}>مسودة</option>
                        <option value="active" {{ 'selected' if contract.status == 'active' else '' }}>نشط</option>
                        <option value="completed" {{ 'selected' if contract.status == 'completed' else '' }}>مكتمل</option>
                        <option value="cancelled" {{ 'selected' if contract.status == 'cancelled' else '' }}>ملغي</option>
                    </select>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Signature Modal -->
<div class="modal fade" id="signatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalTitle">التوقيع الإلكتروني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <canvas id="signature-pad" class="signature-pad" width="460" height="200"></canvas>
                <div class="signature-controls">
                    <button type="button" class="btn btn-secondary" onclick="clearSignature()">
                        <i class="fas fa-eraser me-1"></i>
                        مسح
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveSignature()">
                        <i class="fas fa-save me-1"></i>
                        حفظ التوقيع
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/signature.js') }}"></script>
<script>
    // Initialize signature functionality
    var currentSignatureType = '';
    var contractId = {{ contract.id }};
</script>
{% endblock %}
