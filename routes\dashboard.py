from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from sqlalchemy import func, desc, or_
from datetime import datetime, timedelta
from models import db
from models.user import User
from models.contract import Contract
from models.contract_type import ContractType
from models.notification import Notification

dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@dashboard_bp.route('/')
@login_required
def index():
    """Main dashboard page"""
    # Get statistics
    stats = get_dashboard_stats()
    
    # Get recent contracts
    recent_contracts = Contract.query.order_by(desc(Contract.created_at)).limit(5).all()
    
    # Get contracts by status
    contracts_by_status = db.session.query(
        Contract.status,
        func.count(Contract.id).label('count')
    ).group_by(Contract.status).all()
    
    return render_template('dashboard/index.html', 
                         stats=stats,
                         recent_contracts=recent_contracts,
                         contracts_by_status=contracts_by_status)

@dashboard_bp.route('/users')
@login_required
def users():
    """Users management page (managers only)"""
    if not current_user.is_manager():
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    users = User.query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    return render_template('dashboard/users.html', users=users)

@dashboard_bp.route('/analytics')
@login_required
def analytics():
    """Analytics and reports page"""
    # Contracts created over time
    contracts_over_time = db.session.query(
        func.date(Contract.created_at).label('date'),
        func.count(Contract.id).label('count')
    ).group_by(func.date(Contract.created_at)).order_by('date').all()
    
    # Contracts by type
    contracts_by_type = db.session.query(
        ContractType.name,
        func.count(Contract.id).label('count')
    ).join(Contract).group_by(ContractType.name).all()
    
    # Monthly revenue (if contract values are available)
    monthly_revenue = db.session.query(
        func.strftime('%Y-%m', Contract.created_at).label('month'),
        func.sum(Contract.contract_value).label('total_value')
    ).filter(Contract.contract_value.isnot(None)).group_by('month').all()
    
    return render_template('dashboard/analytics.html',
                         contracts_over_time=contracts_over_time,
                         contracts_by_type=contracts_by_type,
                         monthly_revenue=monthly_revenue)

@dashboard_bp.route('/search')
@login_required
def search():
    """Advanced search contracts"""
    # Get search parameters
    query = request.args.get('q', '')
    contract_type = request.args.get('type', '')
    status = request.args.get('status', '')
    currency = request.args.get('currency', '')
    min_value = request.args.get('min_value', type=float)
    max_value = request.args.get('max_value', type=float)
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    created_by = request.args.get('created_by', '')
    signed = request.args.get('signed', '')

    # Build search query
    contracts_query = Contract.query

    # Filter by user role
    if not current_user.is_manager():
        contracts_query = contracts_query.filter_by(created_by=current_user.id)

    # Text search
    if query:
        contracts_query = contracts_query.filter(
            or_(
                Contract.title.contains(query),
                Contract.first_party_name.contains(query),
                Contract.second_party_name.contains(query),
                Contract.contract_number.contains(query)
            )
        )

    # Contract type filter
    if contract_type:
        contracts_query = contracts_query.filter(Contract.contract_type_id == contract_type)

    # Status filter
    if status:
        contracts_query = contracts_query.filter(Contract.status == status)

    # Currency filter
    if currency:
        contracts_query = contracts_query.filter(Contract.currency == currency)

    # Value range filter
    if min_value is not None:
        contracts_query = contracts_query.filter(Contract.contract_value >= min_value)

    if max_value is not None:
        contracts_query = contracts_query.filter(Contract.contract_value <= max_value)

    # Date range filter
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            contracts_query = contracts_query.filter(Contract.contract_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            contracts_query = contracts_query.filter(Contract.contract_date <= date_to_obj)
        except ValueError:
            pass

    # Created by filter
    if created_by:
        contracts_query = contracts_query.filter(Contract.created_by == created_by)

    # Signed status filter
    if signed == 'signed':
        contracts_query = contracts_query.filter(
            Contract.first_party_signature.isnot(None),
            Contract.second_party_signature.isnot(None),
            Contract.company_seal.isnot(None)
        )
    elif signed == 'unsigned':
        contracts_query = contracts_query.filter(
            or_(
                Contract.first_party_signature.is_(None),
                Contract.second_party_signature.is_(None),
                Contract.company_seal.is_(None)
            )
        )

    # Pagination
    page = request.args.get('page', 1, type=int)
    contracts = contracts_query.order_by(desc(Contract.created_at)).paginate(
        page=page, per_page=15, error_out=False
    )

    # Get data for filters
    contract_types = ContractType.query.filter_by(is_active=True).all()
    users = User.query.filter_by(is_active=True).all() if current_user.is_manager() else []

    search_params = {
        'q': query,
        'type': contract_type,
        'status': status,
        'currency': currency,
        'min_value': min_value,
        'max_value': max_value,
        'date_from': date_from,
        'date_to': date_to,
        'created_by': created_by,
        'signed': signed
    }

    return render_template('dashboard/search.html',
                         contracts=contracts,
                         contract_types=contract_types,
                         users=users,
                         search_params=search_params)

@dashboard_bp.route('/notifications')
@login_required
def notifications():
    """View all notifications"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    notifications = Notification.query.filter_by(user_id=current_user.id)\
                                    .order_by(Notification.created_at.desc())\
                                    .paginate(page=page, per_page=per_page, error_out=False)

    # Mark all as read when viewing the page
    unread_notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).all()

    for notification in unread_notifications:
        notification.is_read = True

    try:
        db.session.commit()
    except:
        db.session.rollback()

    return render_template('dashboard/notifications.html', notifications=notifications)

@dashboard_bp.route('/settings')
@login_required
def settings():
    """System settings (admin only)"""
    if not current_user.is_manager():
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))

    # Get system statistics
    total_contracts = Contract.query.count()
    active_contracts = Contract.query.filter_by(status='active').count()
    total_users = User.query.filter_by(is_active=True).count()
    contract_types = ContractType.query.filter_by(is_active=True).count()

    return render_template('admin/settings.html',
                         total_contracts=total_contracts,
                         active_contracts=active_contracts,
                         total_users=total_users,
                         contract_types=contract_types)

def get_dashboard_stats():
    """Get dashboard statistics"""
    total_contracts = Contract.query.count()
    active_contracts = Contract.query.filter_by(status='active').count()
    draft_contracts = Contract.query.filter_by(status='draft').count()
    completed_contracts = Contract.query.filter_by(status='completed').count()
    
    # Contracts created this month
    current_month = datetime.now().replace(day=1)
    contracts_this_month = Contract.query.filter(
        Contract.created_at >= current_month
    ).count()
    
    # Total contract value
    total_value = db.session.query(func.sum(Contract.contract_value)).scalar() or 0
    
    return {
        'total_contracts': total_contracts,
        'active_contracts': active_contracts,
        'draft_contracts': draft_contracts,
        'completed_contracts': completed_contracts,
        'contracts_this_month': contracts_this_month,
        'total_value': float(total_value)
    }
