import json
from datetime import datetime

class BilingualService:
    def __init__(self):
        self.translations = self._load_translations()
    
    def _load_translations(self):
        """Load translation dictionary"""
        return {
            # Contract Types
            'بيع سيارة': 'Car Sale',
            'شراء عقار': 'Property Purchase',
            'إيجار سكني': 'Residential Lease',
            'إيجار تجاري': 'Commercial Lease',
            'تقسيط': 'Installment',
            'عقد عمل': 'Employment Contract',
            'خدمات استشارية': 'Consulting Services',
            'خدمات صيانة': 'Maintenance Services',
            'مقاولات': 'Contracting',
            'نقل وشحن': 'Transportation & Shipping',
            'تأمين': 'Insurance',
            'قرض شخصي': 'Personal Loan',
            'قرض عقاري': 'Mortgage Loan',
            'وكالة تجارية': 'Commercial Agency',
            'وكالة قانونية': 'Legal Power of Attorney',
            'ترخيص علامة تجارية': 'Trademark License',
            'عقد زواج': 'Marriage Contract',
            'وصية': 'Will/Testament',
            
            # Common Terms
            'دولة قطر': 'State of Qatar',
            'الطرف الأول': 'First Party',
            'الطرف الثاني': 'Second Party',
            'رقم العقد': 'Contract Number',
            'تاريخ العقد': 'Contract Date',
            'التاريخ الهجري': 'Hijri Date',
            'التاريخ الميلادي': 'Gregorian Date',
            'قيمة العقد': 'Contract Value',
            'العملة': 'Currency',
            'ريال قطري': 'Qatari Riyal',
            'الشروط والأحكام': 'Terms and Conditions',
            'التوقيع': 'Signature',
            'ختم الشركة': 'Company Seal',
            'الشاهد': 'Witness',
            'المدة': 'Duration',
            'تاريخ البداية': 'Start Date',
            'تاريخ النهاية': 'End Date',
            
            # Status
            'مسودة': 'Draft',
            'نشط': 'Active',
            'مكتمل': 'Completed',
            'ملغي': 'Cancelled',
            'موقع': 'Signed',
            'غير موقع': 'Unsigned',
            
            # Months (Hijri)
            'محرم': 'Muharram',
            'صفر': 'Safar',
            'ربيع الأول': 'Rabi\' al-awwal',
            'ربيع الثاني': 'Rabi\' al-thani',
            'جمادى الأولى': 'Jumada al-awwal',
            'جمادى الثانية': 'Jumada al-thani',
            'رجب': 'Rajab',
            'شعبان': 'Sha\'ban',
            'رمضان': 'Ramadan',
            'شوال': 'Shawwal',
            'ذو القعدة': 'Dhu al-Qi\'dah',
            'ذو الحجة': 'Dhu al-Hijjah',
            
            # Common Phrases
            'أنه في يوم': 'On this day',
            'تم الاتفاق بين': 'It was agreed between',
            'وقد تم الاتفاق على جميع الشروط والأحكام المذكورة أعلاه': 'All the above-mentioned terms and conditions have been agreed upon',
            'تم تحرير هذا العقد وفقاً لقوانين دولة قطر': 'This contract has been drafted in accordance with the laws of the State of Qatar'
        }
    
    def create_bilingual_contract(self, contract, include_english=True):
        """Create bilingual contract content"""

        print(f"🌐 Creating bilingual contract for {contract.contract_number}")

        if not include_english:
            print("📝 Creating Arabic-only contract")
            return self._create_arabic_only_contract(contract)

        try:
            print("📝 Formatting Arabic content...")
            # Arabic content
            arabic_content = self._format_arabic_content(contract)
            print(f"✅ Arabic content: {len(arabic_content)} chars")

            print("📝 Formatting English content...")
            # English content
            english_content = self._format_english_content(contract)
            print(f"✅ English content: {len(english_content)} chars")

            print("🔗 Combining bilingual content...")
            # Combined bilingual content
            bilingual_content = f"""
            <div class="bilingual-contract">
                <!-- Arabic Version -->
                <div class="arabic-version" style="direction: rtl; text-align: right; margin-bottom: 50px;">
                    <h2 style="text-align: center; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
                        النسخة العربية
                    </h2>
                    {arabic_content}
                </div>

                <!-- English Version -->
                <div class="english-version" style="direction: ltr; text-align: left; margin-top: 50px; border-top: 2px solid #667eea; padding-top: 30px;">
                    <h2 style="text-align: center; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
                        English Version
                    </h2>
                    {english_content}
                </div>

                <!-- Legal Notice -->
                <div class="legal-notice" style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px; text-align: center;">
                    <p style="margin: 0; font-size: 14px; color: #666;">
                        <strong>ملاحظة قانونية:</strong> في حالة وجود تضارب بين النسختين العربية والإنجليزية، تعتبر النسخة العربية هي المرجع الأساسي.
                    </p>
                    <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                        <strong>Legal Notice:</strong> In case of any discrepancy between the Arabic and English versions, the Arabic version shall prevail.
                    </p>
                </div>
            </div>
            """

            print(f"✅ Bilingual content created: {len(bilingual_content)} chars")

            return {
                'success': True,
                'content': bilingual_content,
                'arabic_content': arabic_content,
                'english_content': english_content
            }

        except Exception as e:
            print(f"❌ Bilingual contract error: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'message': f'فشل في إنشاء العقد ثنائي اللغة: {str(e)}'
            }
    
    def _format_arabic_content(self, contract):
        """Format Arabic contract content"""
        
        content = f"""
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; margin-bottom: 10px;">{contract.title}</h1>
            <h2 style="color: #667eea; margin-bottom: 20px;">دولة قطر</h2>
            <p><strong>رقم العقد:</strong> {contract.contract_number}</p>
            <p><strong>تاريخ العقد:</strong> {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else ''}</p>
            <p><strong>التاريخ الهجري:</strong> {self._get_hijri_date(contract.contract_date) if contract.contract_date else ''}</p>
        </div>
        
        <div style="margin: 30px 0;">
            <p>أنه في يوم {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else ''} الموافق {self._get_hijri_date(contract.contract_date) if contract.contract_date else ''} تم الاتفاق بين:</p>
            
            <div style="margin: 20px 0;">
                <p><strong>الطرف الأول:</strong> {contract.first_party_name}</p>
                {f'<p><strong>رقم الهوية:</strong> {contract.first_party_id}</p>' if hasattr(contract, 'first_party_id') and contract.first_party_id else ''}
                {f'<p><strong>الهاتف:</strong> {contract.first_party_phone}</p>' if hasattr(contract, 'first_party_phone') and contract.first_party_phone else ''}
                {f'<p><strong>العنوان:</strong> {contract.first_party_address}</p>' if hasattr(contract, 'first_party_address') and contract.first_party_address else ''}
            </div>
            
            <div style="margin: 20px 0;">
                <p><strong>الطرف الثاني:</strong> {contract.second_party_name}</p>
                {f'<p><strong>رقم الهوية:</strong> {contract.second_party_id}</p>' if hasattr(contract, 'second_party_id') and contract.second_party_id else ''}
                {f'<p><strong>الهاتف:</strong> {contract.second_party_phone}</p>' if hasattr(contract, 'second_party_phone') and contract.second_party_phone else ''}
                {f'<p><strong>العنوان:</strong> {contract.second_party_address}</p>' if hasattr(contract, 'second_party_address') and contract.second_party_address else ''}
            </div>
        </div>
        
        <div style="margin: 30px 0;">
            {contract.content if contract.content else ''}
        </div>
        
        {f'<div style="margin: 20px 0;"><p><strong>قيمة العقد:</strong> {contract.contract_value} {contract.currency}</p></div>' if contract.contract_value else ''}
        
        {f'<div style="margin: 20px 0;"><h4>الشروط والأحكام:</h4><p>{contract.terms_conditions}</p></div>' if contract.terms_conditions else ''}
        
        <div style="margin-top: 40px;">
            <p>وقد تم الاتفاق على جميع الشروط والأحكام المذكورة أعلاه.</p>
            <p>تم تحرير هذا العقد وفقاً لقوانين دولة قطر.</p>
        </div>
        """
        
        return content
    
    def _format_english_content(self, contract):
        """Format English contract content"""
        
        # Translate contract type
        contract_type_en = self.translations.get(contract.contract_type.name if contract.contract_type else '', 
                                                contract.contract_type.name if contract.contract_type else 'Contract')
        
        content = f"""
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; margin-bottom: 10px;">{contract_type_en}</h1>
            <h2 style="color: #667eea; margin-bottom: 20px;">State of Qatar</h2>
            <p><strong>Contract Number:</strong> {contract.contract_number}</p>
            <p><strong>Contract Date:</strong> {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else ''}</p>
            <p><strong>Hijri Date:</strong> {self._get_hijri_date_en(contract.contract_date) if contract.contract_date else ''}</p>
        </div>
        
        <div style="margin: 30px 0;">
            <p>On this day {contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else ''} corresponding to {self._get_hijri_date_en(contract.contract_date) if contract.contract_date else ''}, it was agreed between:</p>
            
            <div style="margin: 20px 0;">
                <p><strong>First Party:</strong> {contract.first_party_name}</p>
                {f'<p><strong>ID Number:</strong> {contract.first_party_id}</p>' if hasattr(contract, 'first_party_id') and contract.first_party_id else ''}
                {f'<p><strong>Phone:</strong> {contract.first_party_phone}</p>' if hasattr(contract, 'first_party_phone') and contract.first_party_phone else ''}
                {f'<p><strong>Address:</strong> {contract.first_party_address}</p>' if hasattr(contract, 'first_party_address') and contract.first_party_address else ''}
            </div>
            
            <div style="margin: 20px 0;">
                <p><strong>Second Party:</strong> {contract.second_party_name}</p>
                {f'<p><strong>ID Number:</strong> {contract.second_party_id}</p>' if hasattr(contract, 'second_party_id') and contract.second_party_id else ''}
                {f'<p><strong>Phone:</strong> {contract.second_party_phone}</p>' if hasattr(contract, 'second_party_phone') and contract.second_party_phone else ''}
                {f'<p><strong>Address:</strong> {contract.second_party_address}</p>' if hasattr(contract, 'second_party_address') and contract.second_party_address else ''}
            </div>
        </div>
        
        <div style="margin: 30px 0;">
            {self._translate_content_to_english(contract.content) if contract.content else ''}
        </div>
        
        {f'<div style="margin: 20px 0;"><p><strong>Contract Value:</strong> {contract.contract_value} {self._translate_currency(contract.currency)}</p></div>' if contract.contract_value else ''}
        
        {f'<div style="margin: 20px 0;"><h4>Terms and Conditions:</h4><p>{self._translate_content_to_english(contract.terms_conditions)}</p></div>' if contract.terms_conditions else ''}
        
        <div style="margin-top: 40px;">
            <p>All the above-mentioned terms and conditions have been agreed upon.</p>
            <p>This contract has been drafted in accordance with the laws of the State of Qatar.</p>
        </div>
        """
        
        return content
    
    def _translate_content_to_english(self, arabic_content):
        """Basic translation of Arabic content to English"""
        if not arabic_content:
            return ''
        
        # This is a basic translation - in production, you would use a proper translation service
        english_content = arabic_content
        
        # Replace common terms
        for arabic, english in self.translations.items():
            english_content = english_content.replace(arabic, english)
        
        return english_content
    
    def _translate_currency(self, currency_code):
        """Translate currency code to English"""
        currency_translations = {
            'QAR': 'Qatari Riyal',
            'SAR': 'Saudi Riyal',
            'AED': 'UAE Dirham',
            'USD': 'US Dollar',
            'EUR': 'Euro',
            'KWD': 'Kuwaiti Dinar',
            'BHD': 'Bahraini Dinar'
        }
        return currency_translations.get(currency_code, currency_code)
    
    def _get_hijri_date(self, gregorian_date):
        """Get Hijri date in Arabic"""
        if not gregorian_date:
            return ''

        try:
            # Simple approximation
            hijri_year = int((gregorian_date.year - 622) * 1.030684) + 1400
            hijri_month = int((gregorian_date.month) * 1.030684) % 12 + 1
            hijri_day = int(gregorian_date.day * 1.030684) % 30 + 1

            # Ensure valid ranges
            hijri_month = max(1, min(12, hijri_month))
            hijri_day = max(1, min(30, hijri_day))

            hijri_months = [
                '', 'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى',
                'جمادى الثانية', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ]

            return f"{hijri_day} {hijri_months[hijri_month]} {hijri_year}هـ"
        except Exception as e:
            print(f"Error converting Hijri date: {e}")
            return gregorian_date.strftime('%Y-%m-%d') if gregorian_date else ''
    
    def _get_hijri_date_en(self, gregorian_date):
        """Get Hijri date in English"""
        if not gregorian_date:
            return ''

        try:
            # Simple approximation
            hijri_year = int((gregorian_date.year - 622) * 1.030684) + 1400
            hijri_month = int((gregorian_date.month) * 1.030684) % 12 + 1
            hijri_day = int(gregorian_date.day * 1.030684) % 30 + 1

            # Ensure valid ranges
            hijri_month = max(1, min(12, hijri_month))
            hijri_day = max(1, min(30, hijri_day))

            hijri_months_en = [
                '', 'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani', 'Jumada al-awwal',
                'Jumada al-thani', 'Rajab', 'Sha\'ban', 'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
            ]

            return f"{hijri_day} {hijri_months_en[hijri_month]} {hijri_year} AH"
        except Exception as e:
            print(f"Error converting Hijri date to English: {e}")
            return gregorian_date.strftime('%Y-%m-%d') if gregorian_date else ''
    
    def _create_arabic_only_contract(self, contract):
        """Create Arabic-only contract"""
        return {
            'success': True,
            'content': self._format_arabic_content(contract),
            'arabic_content': self._format_arabic_content(contract),
            'english_content': None
        }
