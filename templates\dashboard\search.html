{% extends "base.html" %}

{% block title %}البحث المتقدم - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث المتقدم في العقود
                </h1>
            </div>
        </div>
    </div>
    
    <!-- Advanced Search Form -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('dashboard.search') }}">
                <div class="row">
                    <!-- Text Search -->
                    <div class="col-md-6 mb-3">
                        <label for="q" class="form-label">البحث النصي</label>
                        <input type="text" class="form-control" id="q" name="q" 
                               value="{{ search_params.q }}" 
                               placeholder="ابحث في رقم العقد، العنوان، أو أسماء الأطراف...">
                    </div>
                    
                    <!-- Contract Type -->
                    <div class="col-md-6 mb-3">
                        <label for="type" class="form-label">نوع العقد</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">جميع الأنواع</option>
                            {% for contract_type in contract_types %}
                            <option value="{{ contract_type.id }}" 
                                    {{ 'selected' if search_params.type == contract_type.id|string else '' }}>
                                {{ contract_type.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Status -->
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">حالة العقد</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="draft" {{ 'selected' if search_params.status == 'draft' else '' }}>مسودة</option>
                            <option value="active" {{ 'selected' if search_params.status == 'active' else '' }}>نشط</option>
                            <option value="completed" {{ 'selected' if search_params.status == 'completed' else '' }}>مكتمل</option>
                            <option value="cancelled" {{ 'selected' if search_params.status == 'cancelled' else '' }}>ملغي</option>
                        </select>
                    </div>
                    
                    <!-- Currency -->
                    <div class="col-md-3 mb-3">
                        <label for="currency" class="form-label">العملة</label>
                        <select class="form-select" id="currency" name="currency">
                            <option value="">جميع العملات</option>
                            <option value="QAR" {{ 'selected' if search_params.currency == 'QAR' else '' }}>ريال قطري</option>
                            <option value="USD" {{ 'selected' if search_params.currency == 'USD' else '' }}>دولار أمريكي</option>
                            <option value="EUR" {{ 'selected' if search_params.currency == 'EUR' else '' }}>يورو</option>
                            <option value="SAR" {{ 'selected' if search_params.currency == 'SAR' else '' }}>ريال سعودي</option>
                            <option value="AED" {{ 'selected' if search_params.currency == 'AED' else '' }}>درهم إماراتي</option>
                        </select>
                    </div>
                    
                    <!-- Value Range -->
                    <div class="col-md-3 mb-3">
                        <label for="min_value" class="form-label">القيمة من</label>
                        <input type="number" class="form-control" id="min_value" name="min_value" 
                               value="{{ search_params.min_value }}" placeholder="0">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="max_value" class="form-label">القيمة إلى</label>
                        <input type="number" class="form-control" id="max_value" name="max_value" 
                               value="{{ search_params.max_value }}" placeholder="بدون حد أقصى">
                    </div>
                </div>
                
                <div class="row">
                    <!-- Date Range -->
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ search_params.date_from }}">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="{{ search_params.date_to }}">
                    </div>
                    
                    <!-- Created By -->
                    <div class="col-md-3 mb-3">
                        <label for="created_by" class="form-label">منشئ العقد</label>
                        <select class="form-select" id="created_by" name="created_by">
                            <option value="">جميع المستخدمين</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" 
                                    {{ 'selected' if search_params.created_by == user.id|string else '' }}>
                                {{ user.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Signed Status -->
                    <div class="col-md-3 mb-3">
                        <label for="signed" class="form-label">حالة التوقيع</label>
                        <select class="form-select" id="signed" name="signed">
                            <option value="">الكل</option>
                            <option value="signed" {{ 'selected' if search_params.signed == 'signed' else '' }}>موقع</option>
                            <option value="unsigned" {{ 'selected' if search_params.signed == 'unsigned' else '' }}>غير موقع</option>
                        </select>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="{{ url_for('dashboard.search') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            مسح الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Search Results -->
    {% if contracts %}
    <div class="card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                نتائج البحث ({{ contracts.total }} عقد)
            </h6>
            <div>
                <button class="btn btn-sm btn-outline-success" onclick="exportSearchResults('excel')">
                    <i class="fas fa-file-excel me-1"></i>
                    تصدير Excel
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="exportSearchResults('pdf')">
                    <i class="fas fa-file-pdf me-1"></i>
                    تصدير PDF
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>رقم العقد</th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>الطرف الأول</th>
                            <th>الطرف الثاني</th>
                            <th>القيمة</th>
                            <th>الحالة</th>
                            <th>التوقيع</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for contract in contracts.items %}
                        <tr>
                            <td>
                                <input type="checkbox" class="contract-checkbox" value="{{ contract.id }}">
                            </td>
                            <td>
                                <a href="{{ url_for('contracts.view', id=contract.id) }}" class="text-decoration-none">
                                    {{ contract.contract_number }}
                                </a>
                            </td>
                            <td>{{ contract.title }}</td>
                            <td>
                                {% if contract.contract_type %}
                                    <span class="badge bg-info">{{ contract.contract_type.name }}</span>
                                {% endif %}
                            </td>
                            <td>{{ contract.first_party_name }}</td>
                            <td>{{ contract.second_party_name }}</td>
                            <td>
                                {% if contract.contract_value %}
                                    {{ contract.contract_value|currency(contract.currency) }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if contract.status == 'active' else 'warning' if contract.status == 'draft' else 'info' if contract.status == 'completed' else 'secondary' }}">
                                    {{ contract.get_status_display() }}
                                </span>
                            </td>
                            <td>
                                {% if contract.is_signed() %}
                                    <span class="text-success"><i class="fas fa-check-circle"></i> موقع</span>
                                {% else %}
                                    <span class="text-warning"><i class="fas fa-clock"></i> غير موقع</span>
                                {% endif %}
                            </td>
                            <td>{{ contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('contracts.view', id=contract.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if contract.can_edit(current_user) %}
                                    <a href="{{ url_for('contracts.edit', id=contract.id) }}" 
                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if contracts.pages > 1 %}
            <nav aria-label="صفحات نتائج البحث">
                <ul class="pagination justify-content-center">
                    {% if contracts.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('dashboard.search', page=contracts.prev_num, **search_params) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in contracts.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != contracts.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('dashboard.search', page=page_num, **search_params) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if contracts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('dashboard.search', page=contracts.next_num, **search_params) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
    
    <!-- Bulk Actions -->
    <div class="card shadow mt-4" id="bulkActionsCard" style="display: none;">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">إجراءات متعددة</h6>
        </div>
        <div class="card-body">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="bulkAction('export_pdf')">
                    <i class="fas fa-file-pdf me-1"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="bulkAction('export_excel')">
                    <i class="fas fa-file-excel me-1"></i>
                    تصدير Excel
                </button>
                {% if current_user.is_manager() %}
                <button type="button" class="btn btn-outline-warning" onclick="bulkAction('change_status')">
                    <i class="fas fa-edit me-1"></i>
                    تغيير الحالة
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="bulkAction('delete')">
                    <i class="fas fa-trash me-1"></i>
                    حذف
                </button>
                {% endif %}
            </div>
            <span class="ms-3 text-muted" id="selectedCount">0 عقد محدد</span>
        </div>
    </div>
    
    {% elif request.args.get('q') or request.args.get('type') or request.args.get('status') %}
    <!-- No Results -->
    <div class="card shadow">
        <div class="card-body text-center py-5">
            <i class="fas fa-search fa-5x text-gray-300 mb-4"></i>
            <h4 class="text-gray-600">لا توجد نتائج</h4>
            <p class="text-gray-500 mb-4">لم يتم العثور على عقود تطابق معايير البحث</p>
            <a href="{{ url_for('dashboard.search') }}" class="btn btn-primary">
                <i class="fas fa-times me-1"></i>
                مسح الفلاتر
            </a>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.contract-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

// Update bulk actions visibility
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.contract-checkbox:checked');
    const bulkCard = document.getElementById('bulkActionsCard');
    const selectedCount = document.getElementById('selectedCount');
    
    if (checkboxes.length > 0) {
        bulkCard.style.display = 'block';
        selectedCount.textContent = `${checkboxes.length} عقد محدد`;
    } else {
        bulkCard.style.display = 'none';
    }
}

// Add event listeners to checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.contract-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
});

// Export search results
function exportSearchResults(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.location.href = `{{ url_for('dashboard.search') }}?${params.toString()}`;
}

// Bulk actions
function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.contract-checkbox:checked'))
                            .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('يرجى اختيار عقد واحد على الأقل');
        return;
    }
    
    switch(action) {
        case 'export_pdf':
            // Implement bulk PDF export
            break;
        case 'export_excel':
            // Implement bulk Excel export
            break;
        case 'change_status':
            // Show status change modal
            break;
        case 'delete':
            if (confirm(`هل أنت متأكد من حذف ${selectedIds.length} عقد؟`)) {
                // Implement bulk delete
            }
            break;
    }
}
</script>

<style>
.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-600 {
    color: #6e707e !important;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
</style>
{% endblock %}
