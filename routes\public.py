from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from models.contract import Contract
from utils.qr_service import QRService

public_bp = Blueprint('public', __name__)

@public_bp.route('/verify')
def verify_page():
    """Public contract verification page"""
    return render_template('public/verify.html')

@public_bp.route('/verify/<contract_number>')
def verify_contract(contract_number):
    """Direct contract verification by number"""
    contract = Contract.query.filter_by(contract_number=contract_number).first()
    
    if not contract:
        return render_template('public/verify.html', 
                             error=f'العقد رقم {contract_number} غير موجود في النظام')
    
    # Create verification data
    verification_data = {
        'success': True,
        'verified': True,
        'contract': {
            'id': contract.id,
            'contract_number': contract.contract_number,
            'title': contract.title,
            'status': contract.status,
            'first_party_name': contract.first_party_name,
            'second_party_name': contract.second_party_name,
            'contract_date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else None,
            'contract_value': contract.contract_value,
            'currency': contract.currency,
            'is_signed': contract.is_signed()
        }
    }
    
    return render_template('public/verify.html', verification_data=verification_data)

@public_bp.route('/qr/<contract_number>')
def qr_redirect(contract_number):
    """QR code redirect to verification"""
    return redirect(url_for('public.verify_contract', contract_number=contract_number))

@public_bp.route('/test-api')
def test_api():
    """Test API page for debugging"""
    return render_template('test_api.html')
