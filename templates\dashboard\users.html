{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة العقود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة المستخدمين
                </h1>
                <div>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">قائمة المستخدمين</h6>
        </div>
        <div class="card-body">
            {% if users.items %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>{{ user.full_name }}</td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if user.is_manager() else 'secondary' }}">
                                        {{ 'مدير' if user.is_manager() else 'موظف' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                        {{ 'نشط' if user.is_active else 'غير نشط' }}
                                    </span>
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '' }}</td>
                                <td>
                                    {% if user.last_login %}
                                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">لم يسجل دخول</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if user.id != current_user.id %}
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})"
                                                title="{{ 'إلغاء تفعيل' if user.is_active else 'تفعيل' }}">
                                            <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="resetPassword({{ user.id }})" title="إعادة تعيين كلمة المرور">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if users.pages > 1 %}
                <nav aria-label="صفحات المستخدمين">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('dashboard.users', page=users.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.users', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('dashboard.users', page=users.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-gray-300 mb-4"></i>
                    <h4 class="text-gray-600">لا يوجد مستخدمون</h4>
                    <p class="text-gray-500 mb-4">لم يتم إنشاء أي مستخدمين بعد</p>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId, isActive) {
    const action = isActive ? 'إلغاء تفعيل' : 'تفعيل';
    if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
        // Here you would make an AJAX call to toggle user status
        showAlert(`تم ${action} المستخدم بنجاح`, 'success');
        setTimeout(() => location.reload(), 1000);
    }
}

function resetPassword(userId) {
    if (confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')) {
        // Here you would make an AJAX call to reset password
        showAlert('تم إعادة تعيين كلمة المرور بنجاح', 'success');
    }
}
</script>

<style>
.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-600 {
    color: #6e707e !important;
}
</style>
{% endblock %}
