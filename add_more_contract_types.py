#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to add more specialized contract types for Qatar
"""

from app import create_app
from models import db
from models.contract_type import ContractType

def add_specialized_contract_types():
    """Add more specialized contract types for Qatar"""
    
    specialized_types = [
        {
            'name': 'خدمات صيانة',
            'description': 'عقد خدمات صيانة في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد خدمات صيانة</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (العميل):</strong> {{client_name}}</p>
            <p><strong>الطرف الثاني (مقدم الخدمة):</strong> {{service_provider}}</p>
            <p><strong>نوع الصيانة:</strong> {{maintenance_type}}</p>
            <p><strong>نطاق الخدمة:</strong> {{service_scope}}</p>
            <p><strong>مدة العقد:</strong> {{contract_duration}}</p>
            <p><strong>تكلفة الصيانة:</strong> {{maintenance_cost}} ريال قطري</p>
            <p><strong>جدولة الصيانة:</strong> {{maintenance_schedule}}</p>
            ''',
            'fields': {
                'client_name': 'اسم العميل',
                'service_provider': 'مقدم الخدمة',
                'maintenance_type': 'نوع الصيانة',
                'service_scope': 'نطاق الخدمة',
                'contract_duration': 'مدة العقد',
                'maintenance_cost': 'تكلفة الصيانة',
                'maintenance_schedule': 'جدولة الصيانة',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'نقل وشحن',
            'description': 'عقد نقل وشحن في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد نقل وشحن</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (العميل):</strong> {{client_name}}</p>
            <p><strong>الطرف الثاني (شركة النقل):</strong> {{transport_company}}</p>
            <p><strong>رخصة النقل:</strong> {{transport_license}}</p>
            <p><strong>نوع البضاعة:</strong> {{cargo_type}}</p>
            <p><strong>الوزن:</strong> {{weight}} كيلوجرام</p>
            <p><strong>نقطة الانطلاق:</strong> {{origin}}</p>
            <p><strong>نقطة الوصول:</strong> {{destination}}</p>
            <p><strong>تكلفة النقل:</strong> {{transport_cost}} ريال قطري</p>
            ''',
            'fields': {
                'client_name': 'اسم العميل',
                'transport_company': 'شركة النقل',
                'transport_license': 'رخصة النقل',
                'cargo_type': 'نوع البضاعة',
                'weight': 'الوزن',
                'origin': 'نقطة الانطلاق',
                'destination': 'نقطة الوصول',
                'transport_cost': 'تكلفة النقل',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'تأمين',
            'description': 'عقد تأمين في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد تأمين</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (المؤمن له):</strong> {{insured_name}}</p>
            <p><strong>الطرف الثاني (شركة التأمين):</strong> {{insurance_company}}</p>
            <p><strong>رخصة التأمين:</strong> {{insurance_license}}</p>
            <p><strong>نوع التأمين:</strong> {{insurance_type}}</p>
            <p><strong>مبلغ التأمين:</strong> {{insurance_amount}} ريال قطري</p>
            <p><strong>القسط السنوي:</strong> {{annual_premium}} ريال قطري</p>
            <p><strong>مدة التأمين:</strong> {{insurance_period}}</p>
            <p><strong>التغطية:</strong> {{coverage_details}}</p>
            ''',
            'fields': {
                'insured_name': 'اسم المؤمن له',
                'insurance_company': 'شركة التأمين',
                'insurance_license': 'رخصة التأمين',
                'insurance_type': 'نوع التأمين',
                'insurance_amount': 'مبلغ التأمين',
                'annual_premium': 'القسط السنوي',
                'insurance_period': 'مدة التأمين',
                'coverage_details': 'تفاصيل التغطية',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'قرض شخصي',
            'description': 'عقد قرض شخصي في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد قرض شخصي</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (المقرض):</strong> {{lender_name}}</p>
            <p><strong>الطرف الثاني (المقترض):</strong> {{borrower_name}}</p>
            <p><strong>رقم هوية المقترض:</strong> {{borrower_id}}</p>
            <p><strong>مبلغ القرض:</strong> {{loan_amount}} ريال قطري</p>
            <p><strong>معدل الفائدة:</strong> {{interest_rate}}% سنوياً</p>
            <p><strong>مدة السداد:</strong> {{repayment_period}}</p>
            <p><strong>القسط الشهري:</strong> {{monthly_payment}} ريال قطري</p>
            <p><strong>الضمانات:</strong> {{guarantees}}</p>
            ''',
            'fields': {
                'lender_name': 'اسم المقرض',
                'borrower_name': 'اسم المقترض',
                'borrower_id': 'رقم هوية المقترض',
                'loan_amount': 'مبلغ القرض',
                'interest_rate': 'معدل الفائدة',
                'repayment_period': 'مدة السداد',
                'monthly_payment': 'القسط الشهري',
                'guarantees': 'الضمانات',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'قرض عقاري',
            'description': 'عقد قرض عقاري في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد قرض عقاري</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (البنك/المقرض):</strong> {{bank_name}}</p>
            <p><strong>الطرف الثاني (المقترض):</strong> {{borrower_name}}</p>
            <p><strong>العقار المرهون:</strong> {{property_description}}</p>
            <p><strong>قيمة العقار:</strong> {{property_value}} ريال قطري</p>
            <p><strong>مبلغ القرض:</strong> {{loan_amount}} ريال قطري</p>
            <p><strong>نسبة التمويل:</strong> {{financing_ratio}}%</p>
            <p><strong>معدل الفائدة:</strong> {{interest_rate}}% سنوياً</p>
            <p><strong>مدة السداد:</strong> {{repayment_period}} سنة</p>
            <p><strong>القسط الشهري:</strong> {{monthly_payment}} ريال قطري</p>
            ''',
            'fields': {
                'bank_name': 'اسم البنك',
                'borrower_name': 'اسم المقترض',
                'property_description': 'وصف العقار',
                'property_value': 'قيمة العقار',
                'loan_amount': 'مبلغ القرض',
                'financing_ratio': 'نسبة التمويل',
                'interest_rate': 'معدل الفائدة',
                'repayment_period': 'مدة السداد',
                'monthly_payment': 'القسط الشهري',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'وكالة تجارية',
            'description': 'عقد وكالة تجارية في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد وكالة تجارية</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (الموكل):</strong> {{principal_company}}</p>
            <p><strong>الطرف الثاني (الوكيل التجاري):</strong> {{agent_company}}</p>
            <p><strong>السجل التجاري للوكيل:</strong> {{agent_commercial_register}}</p>
            <p><strong>المنتجات/الخدمات:</strong> {{products_services}}</p>
            <p><strong>المنطقة الجغرافية:</strong> {{territory}}</p>
            <p><strong>مدة الوكالة:</strong> {{agency_duration}}</p>
            <p><strong>العمولة:</strong> {{commission_rate}}%</p>
            <p><strong>الحد الأدنى للمبيعات:</strong> {{minimum_sales}} ريال قطري</p>
            ''',
            'fields': {
                'principal_company': 'الشركة الموكلة',
                'agent_company': 'الوكيل التجاري',
                'agent_commercial_register': 'السجل التجاري للوكيل',
                'products_services': 'المنتجات/الخدمات',
                'territory': 'المنطقة الجغرافية',
                'agency_duration': 'مدة الوكالة',
                'commission_rate': 'معدل العمولة',
                'minimum_sales': 'الحد الأدنى للمبيعات',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'ترخيص علامة تجارية',
            'description': 'عقد ترخيص علامة تجارية في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد ترخيص علامة تجارية</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (مالك العلامة):</strong> {{trademark_owner}}</p>
            <p><strong>الطرف الثاني (المرخص له):</strong> {{licensee}}</p>
            <p><strong>العلامة التجارية:</strong> {{trademark_name}}</p>
            <p><strong>رقم تسجيل العلامة:</strong> {{trademark_registration}}</p>
            <p><strong>نطاق الترخيص:</strong> {{license_scope}}</p>
            <p><strong>المنطقة الجغرافية:</strong> {{geographical_area}}</p>
            <p><strong>مدة الترخيص:</strong> {{license_duration}}</p>
            <p><strong>رسوم الترخيص:</strong> {{license_fee}} ريال قطري</p>
            <p><strong>نسبة الإتاوة:</strong> {{royalty_rate}}%</p>
            ''',
            'fields': {
                'trademark_owner': 'مالك العلامة التجارية',
                'licensee': 'المرخص له',
                'trademark_name': 'اسم العلامة التجارية',
                'trademark_registration': 'رقم تسجيل العلامة',
                'license_scope': 'نطاق الترخيص',
                'geographical_area': 'المنطقة الجغرافية',
                'license_duration': 'مدة الترخيص',
                'license_fee': 'رسوم الترخيص',
                'royalty_rate': 'نسبة الإتاوة',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'عقد زواج',
            'description': 'عقد زواج في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد زواج</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم عقد القران بين:</p>
            <p><strong>الزوج:</strong> {{groom_name}}</p>
            <p><strong>رقم الهوية:</strong> {{groom_id}}</p>
            <p><strong>الزوجة:</strong> {{bride_name}}</p>
            <p><strong>رقم الهوية:</strong> {{bride_id}}</p>
            <p><strong>ولي الزوجة:</strong> {{bride_guardian}}</p>
            <p><strong>المهر المعجل:</strong> {{immediate_dower}} ريال قطري</p>
            <p><strong>المهر المؤجل:</strong> {{deferred_dower}} ريال قطري</p>
            <p><strong>الشاهد الأول:</strong> {{witness_1}}</p>
            <p><strong>الشاهد الثاني:</strong> {{witness_2}}</p>
            <p><strong>المأذون:</strong> {{marriage_officer}}</p>
            ''',
            'fields': {
                'groom_name': 'اسم الزوج',
                'groom_id': 'رقم هوية الزوج',
                'bride_name': 'اسم الزوجة',
                'bride_id': 'رقم هوية الزوجة',
                'bride_guardian': 'ولي الزوجة',
                'immediate_dower': 'المهر المعجل',
                'deferred_dower': 'المهر المؤجل',
                'witness_1': 'الشاهد الأول',
                'witness_2': 'الشاهد الثاني',
                'marriage_officer': 'المأذون',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'وصية',
            'description': 'وصية في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">وصية</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} أوصى:</p>
            <p><strong>الموصي:</strong> {{testator_name}}</p>
            <p><strong>رقم الهوية:</strong> {{testator_id}}</p>
            <p><strong>العنوان:</strong> {{testator_address}}</p>
            <p><strong>الموصى له:</strong> {{beneficiary_name}}</p>
            <p><strong>صلة القرابة:</strong> {{relationship}}</p>
            <p><strong>الموصى به:</strong> {{bequest_description}}</p>
            <p><strong>قيمة الوصية:</strong> {{bequest_value}} ريال قطري</p>
            <p><strong>الشاهد الأول:</strong> {{witness_1}}</p>
            <p><strong>الشاهد الثاني:</strong> {{witness_2}}</p>
            <p>وقد أقر الموصي بأنه في كامل قواه العقلية وأن هذه وصيته الأخيرة.</p>
            ''',
            'fields': {
                'testator_name': 'اسم الموصي',
                'testator_id': 'رقم هوية الموصي',
                'testator_address': 'عنوان الموصي',
                'beneficiary_name': 'اسم الموصى له',
                'relationship': 'صلة القرابة',
                'bequest_description': 'وصف الوصية',
                'bequest_value': 'قيمة الوصية',
                'witness_1': 'الشاهد الأول',
                'witness_2': 'الشاهد الثاني',
                'hijri_date': 'التاريخ الهجري'
            }
        }
    ]
    
    # Add contract types to database
    for contract_type_data in specialized_types:
        # Check if contract type already exists
        existing = ContractType.query.filter_by(name=contract_type_data['name']).first()
        if not existing:
            contract_type = ContractType(
                name=contract_type_data['name'],
                description=contract_type_data['description'],
                template=contract_type_data['template'],
                fields=contract_type_data['fields']
            )
            db.session.add(contract_type)
            print(f"Added specialized contract type: {contract_type_data['name']}")
        else:
            print(f"Specialized contract type already exists: {contract_type_data['name']}")
    
    try:
        db.session.commit()
        print("Successfully added all specialized contract types!")
    except Exception as e:
        db.session.rollback()
        print(f"Error adding specialized contract types: {e}")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        add_specialized_contract_types()
