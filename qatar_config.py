# -*- coding: utf-8 -*-
"""
Qatar-specific configuration for the Contract Management System
إعدادات خاصة بدولة قطر لنظام إدارة العقود
"""

# Qatar Contract Types with detailed templates
QATAR_CONTRACT_TYPES = {
    'بيع سيارة': {
        'description': 'عقد بيع سيارة في دولة قطر',
        'required_fields': ['seller_name', 'buyer_name', 'car_type', 'plate_number', 'price'],
        'optional_fields': ['car_model', 'car_year', 'chassis_number', 'seller_id', 'buyer_id'],
        'legal_requirements': [
            'يجب أن يكون البائع مالك السيارة أو وكيل عنه',
            'يجب تسجيل العقد في إدارة المرور',
            'يجب دفع الرسوم المطلوبة'
        ]
    },
    'إيجار سكني': {
        'description': 'عقد إيجار عقار سكني في دولة قطر',
        'required_fields': ['landlord_name', 'tenant_name', 'property_description', 'monthly_rent'],
        'optional_fields': ['area', 'plot_number', 'annual_rent', 'security_deposit'],
        'legal_requirements': [
            'يجب تسجيل العقد في لجنة فض منازعات الإيجار',
            'يجب أن تكون مدة العقد محددة',
            'يجب تحديد مسؤوليات كل طرف'
        ]
    },
    'عقد عمل': {
        'description': 'عقد عمل في دولة قطر',
        'required_fields': ['employer_name', 'employee_name', 'job_title', 'basic_salary'],
        'optional_fields': ['allowances', 'total_salary', 'contract_duration', 'working_hours'],
        'legal_requirements': [
            'يجب أن يكون العقد باللغة العربية',
            'يجب تحديد فترة التجربة',
            'يجب تحديد الإجازات والمزايا'
        ]
    }
}

# Qatar Government Entities
QATAR_GOVERNMENT_ENTITIES = [
    'وزارة الداخلية',
    'وزارة العدل',
    'وزارة التجارة والصناعة',
    'وزارة البلدية والبيئة',
    'وزارة المواصلات والاتصالات',
    'وزارة التنمية الإدارية والعمل والشؤون الاجتماعية',
    'وزارة الصحة العامة',
    'وزارة التعليم والتعليم العالي',
    'وزارة الأوقاف والشؤون الإسلامية',
    'وزارة الثقافة والرياضة',
    'ديوان المحاسبة',
    'النيابة العامة',
    'المحكمة العليا'
]

# Qatar Areas/Municipalities
QATAR_AREAS = [
    'الدوحة',
    'الريان',
    'الوكرة',
    'أم صلال',
    'الخور',
    'الشمال',
    'الضعاين',
    'الشحانية'
]

# Qatar Business Types
QATAR_BUSINESS_TYPES = [
    'تجارة عامة',
    'مقاولات',
    'استشارات',
    'خدمات',
    'صناعة',
    'زراعة',
    'نقل ومواصلات',
    'سياحة وفنادق',
    'تعليم',
    'صحة',
    'تكنولوجيا المعلومات',
    'إعلام ونشر',
    'عقارات',
    'مالية ومصرفية'
]

# Qatar Currency Settings
QATAR_CURRENCY = {
    'code': 'QAR',
    'name': 'ريال قطري',
    'symbol': 'ر.ق',
    'decimal_places': 2,
    'format': '{amount} {symbol}'
}

# Qatar Legal Requirements
QATAR_LEGAL_REQUIREMENTS = {
    'contract_language': 'العربية',
    'witness_required': True,
    'notarization_required': False,  # Depends on contract type
    'registration_required': True,   # For certain contract types
    'stamp_duty': True,
    'minimum_contract_value_for_registration': 10000  # QAR
}

# Qatar Date Formats
QATAR_DATE_FORMATS = {
    'gregorian': '%Y-%m-%d',
    'hijri': '%d %B %Y هـ',
    'display': '%d/%m/%Y'
}

# Qatar ID Formats
QATAR_ID_FORMATS = {
    'qatari_id': r'^\d{11}$',           # 11 digits for Qatari ID
    'residence_permit': r'^\d{11}$',    # 11 digits for residence permit
    'commercial_register': r'^\d{1,8}$' # Up to 8 digits for commercial register
}

# Qatar Contract Templates with legal compliance
QATAR_LEGAL_TEMPLATES = {
    'header': '''
    <div style="text-align: center; margin-bottom: 30px;">
        <h1>دولة قطر</h1>
        <h2>{contract_type}</h2>
        <p>رقم العقد: {contract_number}</p>
        <p>التاريخ الميلادي: {gregorian_date}</p>
        <p>التاريخ الهجري: {hijri_date}</p>
    </div>
    ''',
    
    'footer': '''
    <div style="margin-top: 50px;">
        <p style="text-align: center; font-weight: bold;">
            تم تحرير هذا العقد وفقاً لقوانين دولة قطر
        </p>
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
            <div style="text-align: center;">
                <p>توقيع الطرف الأول</p>
                <div style="height: 80px; border-bottom: 1px solid #000; margin: 20px 0;"></div>
                <p>التاريخ: ___________</p>
            </div>
            <div style="text-align: center;">
                <p>توقيع الطرف الثاني</p>
                <div style="height: 80px; border-bottom: 1px solid #000; margin: 20px 0;"></div>
                <p>التاريخ: ___________</p>
            </div>
            <div style="text-align: center;">
                <p>الشاهد</p>
                <div style="height: 80px; border-bottom: 1px solid #000; margin: 20px 0;"></div>
                <p>التاريخ: ___________</p>
            </div>
        </div>
    </div>
    '''
}

# Qatar Validation Rules
QATAR_VALIDATION_RULES = {
    'contract_value': {
        'min': 1,
        'max': 999999999,
        'currency': 'QAR'
    },
    'contract_duration': {
        'min_days': 1,
        'max_years': 10
    },
    'party_names': {
        'min_length': 2,
        'max_length': 100,
        'allowed_chars': r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\-\.]+$'
    }
}

# Qatar Holidays (approximate - should be updated annually)
QATAR_HOLIDAYS = [
    {'name': 'رأس السنة الميلادية', 'date': '01-01'},
    {'name': 'اليوم الوطني لدولة قطر', 'date': '18-12'},
    {'name': 'يوم الرياضة', 'date': '09-02'},  # Second Tuesday of February
    # Islamic holidays (dates vary each year)
    {'name': 'عيد الفطر', 'type': 'islamic'},
    {'name': 'عيد الأضحى', 'type': 'islamic'},
    {'name': 'المولد النبوي', 'type': 'islamic'},
    {'name': 'الإسراء والمعراج', 'type': 'islamic'}
]

# Qatar Contact Information for Legal Support
QATAR_LEGAL_CONTACTS = {
    'ministry_of_justice': {
        'name': 'وزارة العدل',
        'phone': '+974 4444 8888',
        'website': 'https://www.moj.gov.qa'
    },
    'rental_disputes_committee': {
        'name': 'لجنة فض منازعات الإيجار',
        'phone': '+974 4444 7777',
        'website': 'https://www.mme.gov.qa'
    },
    'commercial_registration': {
        'name': 'السجل التجاري',
        'phone': '+974 4444 9999',
        'website': 'https://www.moci.gov.qa'
    }
}

# Export all configurations
__all__ = [
    'QATAR_CONTRACT_TYPES',
    'QATAR_GOVERNMENT_ENTITIES', 
    'QATAR_AREAS',
    'QATAR_BUSINESS_TYPES',
    'QATAR_CURRENCY',
    'QATAR_LEGAL_REQUIREMENTS',
    'QATAR_DATE_FORMATS',
    'QATAR_ID_FORMATS',
    'QATAR_LEGAL_TEMPLATES',
    'QATAR_VALIDATION_RULES',
    'QATAR_HOLIDAYS',
    'QATAR_LEGAL_CONTACTS'
]
