import os
from datetime import timedelta

class Config:
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///contract_management.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload configuration
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Application settings
    CONTRACTS_PER_PAGE = 10
    SUPPORTED_LANGUAGES = ['ar', 'en']
    DEFAULT_LANGUAGE = 'ar'
    
    # Contract types for Qatar
    CONTRACT_TYPES = [
        'بيع سيارة',           # Car Sale
        'شراء عقار',          # Property Purchase
        'إيجار سكني',         # Residential Lease
        'إيجار تجاري',        # Commercial Lease
        'تقسيط',             # Installment
        'عقد عمل',            # Employment Contract
        'خدمات استشارية',     # Consulting Services
        'خدمات صيانة',        # Maintenance Services
        'شراكة تجارية',       # Business Partnership
        'توريد وتركيب',       # Supply & Installation
        'مقاولات',           # Contracting
        'نقل وشحن',          # Transportation & Shipping
        'تأمين',             # Insurance
        'قرض شخصي',          # Personal Loan
        'قرض عقاري',         # Mortgage Loan
        'وكالة تجارية',       # Commercial Agency
        'ترخيص علامة تجارية', # Trademark License
        'عقد زواج',          # Marriage Contract
        'وصية',              # Will/Testament
        'وكالة قانونية',      # Legal Power of Attorney
        'أخرى'               # Other
    ]
    
    # User roles
    USER_ROLES = {
        'manager': 'مدير',
        'employee': 'موظف'
    }

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
