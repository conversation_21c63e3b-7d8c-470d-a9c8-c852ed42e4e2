import os
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.ns import qn

def generate_word(contract):
    """Generate Word document from contract data"""
    
    # Create new document
    doc = Document()
    
    # Set document direction to RTL
    sections = doc.sections
    for section in sections:
        sectPr = section._sectPr
        bidi = sectPr.find(qn('w:bidi'))
        if bidi is None:
            bidi = sectPr.makeelement(qn('w:bidi'))
            sectPr.append(bidi)
    
    # Add header
    add_header(doc, contract)
    
    # Add parties information
    add_parties_info(doc, contract)
    
    # Add contract content
    add_contract_content(doc, contract)
    
    # Add terms and conditions
    if contract.terms_conditions:
        add_terms_conditions(doc, contract)
    
    # Add signature section
    add_signature_section(doc)
    
    # Add footer
    add_footer(doc)
    
    # Save document
    output_dir = os.path.join('static', 'exports', 'word')
    os.makedirs(output_dir, exist_ok=True)
    
    filename = f"{contract.contract_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
    file_path = os.path.join(output_dir, filename)
    
    doc.save(file_path)
    return file_path

def add_header(doc, contract):
    """Add document header"""
    # Title
    title = doc.add_heading(contract.title, level=1)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Contract number and date
    info_para = doc.add_paragraph()
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    info_para.add_run(f"رقم العقد: {contract.contract_number}\n")
    if contract.contract_date:
        info_para.add_run(f"تاريخ العقد: {contract.contract_date.strftime('%Y-%m-%d')}")
    
    # Add spacing
    doc.add_paragraph()

def add_parties_info(doc, contract):
    """Add parties information"""
    # Create table for parties
    table = doc.add_table(rows=2, cols=2)
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # First party
    cell1 = table.cell(0, 0)
    cell1_para = cell1.paragraphs[0]
    cell1_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    cell1_para.add_run("الطرف الأول:").bold = True
    cell1.add_paragraph(f"الاسم: {contract.first_party_name}")
    if contract.first_party_id:
        cell1.add_paragraph(f"رقم الهوية: {contract.first_party_id}")
    if contract.first_party_address:
        cell1.add_paragraph(f"العنوان: {contract.first_party_address}")
    if contract.first_party_phone:
        cell1.add_paragraph(f"الهاتف: {contract.first_party_phone}")
    
    # Second party
    cell2 = table.cell(0, 1)
    cell2_para = cell2.paragraphs[0]
    cell2_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    cell2_para.add_run("الطرف الثاني:").bold = True
    cell2.add_paragraph(f"الاسم: {contract.second_party_name}")
    if contract.second_party_id:
        cell2.add_paragraph(f"رقم الهوية: {contract.second_party_id}")
    if contract.second_party_address:
        cell2.add_paragraph(f"العنوان: {contract.second_party_address}")
    if contract.second_party_phone:
        cell2.add_paragraph(f"الهاتف: {contract.second_party_phone}")
    
    # Add spacing
    doc.add_paragraph()

def add_contract_content(doc, contract):
    """Add contract content"""
    # Process content with variables
    content = contract.content
    
    # Replace variables
    variables = {
        'date': contract.contract_date.strftime('%Y-%m-%d') if contract.contract_date else '',
        'first_party_name': contract.first_party_name,
        'second_party_name': contract.second_party_name,
        'contract_number': contract.contract_number,
        'contract_value': str(contract.contract_value) if contract.contract_value else '',
        'currency': contract.currency
    }
    
    # Add custom fields
    if contract.custom_fields:
        variables.update(contract.custom_fields)
    
    # Replace variables in content
    for key, value in variables.items():
        content = content.replace(f'{{{{{key}}}}}', str(value))
    
    # Add content heading
    content_heading = doc.add_heading("نص العقد:", level=2)
    content_heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    
    # Add content (simplified HTML to text conversion)
    # Remove HTML tags for Word document
    import re
    clean_content = re.sub('<.*?>', '', content)
    clean_content = clean_content.replace('&nbsp;', ' ')
    clean_content = clean_content.replace('&lt;', '<')
    clean_content = clean_content.replace('&gt;', '>')
    clean_content = clean_content.replace('&amp;', '&')
    
    content_para = doc.add_paragraph(clean_content)
    content_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    
    # Add contract value if available
    if contract.contract_value:
        value_para = doc.add_paragraph()
        value_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        value_para.add_run("قيمة العقد: ").bold = True
        value_para.add_run(f"{contract.contract_value} {contract.currency}")

def add_terms_conditions(doc, contract):
    """Add terms and conditions"""
    doc.add_paragraph()
    terms_heading = doc.add_heading("الشروط والأحكام:", level=2)
    terms_heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    
    # Clean HTML from terms
    import re
    clean_terms = re.sub('<.*?>', '', contract.terms_conditions)
    clean_terms = clean_terms.replace('&nbsp;', ' ')
    clean_terms = clean_terms.replace('&lt;', '<')
    clean_terms = clean_terms.replace('&gt;', '>')
    clean_terms = clean_terms.replace('&amp;', '&')
    
    terms_para = doc.add_paragraph(clean_terms)
    terms_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

def add_signature_section(doc):
    """Add signature section"""
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Signatures heading
    sig_heading = doc.add_heading("التوقيعات:", level=2)
    sig_heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    
    # Create signature table
    sig_table = doc.add_table(rows=3, cols=3)
    sig_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Headers
    headers = ["توقيع الطرف الأول", "توقيع الطرف الثاني", "ختم الشركة"]
    for i, header in enumerate(headers):
        cell = sig_table.cell(0, i)
        cell.paragraphs[0].add_run(header).bold = True
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Signature spaces
    for i in range(3):
        cell = sig_table.cell(1, i)
        cell.paragraphs[0].add_run("\n\n\n")  # Space for signature
        
    # Names/labels
    labels = ["الاسم:", "الاسم:", "التاريخ:"]
    for i, label in enumerate(labels):
        cell = sig_table.cell(2, i)
        cell.paragraphs[0].add_run(label)
        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

def add_footer(doc):
    """Add document footer"""
    doc.add_paragraph()
    doc.add_paragraph()
    
    footer_para = doc.add_paragraph()
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_para.add_run("تم إنشاء هذا العقد بواسطة نظام إدارة العقود الإلكترونية\n")
    footer_para.add_run(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Make footer text smaller
    for run in footer_para.runs:
        run.font.size = Pt(10)
