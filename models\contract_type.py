from datetime import datetime
from . import db

class ContractType(db.Model):
    __tablename__ = 'contract_types'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    template = db.Column(db.Text)  # Default template for this contract type
    fields = db.Column(db.JSON)  # Custom fields for this contract type
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contracts = db.relationship('Contract', back_populates='contract_type', lazy=True)
    
    def __init__(self, name, description=None, template=None, fields=None):
        self.name = name
        self.description = description
        self.template = template
        self.fields = fields or {}
    
    def to_dict(self):
        """Convert contract type to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'template': self.template,
            'fields': self.fields,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def get_default_types():
        """Get default contract types for Qatar"""
        return [
            {
                'name': 'بيع سيارة',
                'description': 'عقد بيع سيارة في دولة قطر',
                'template': '''
                <h2 style="text-align: center;">عقد بيع سيارة</h2>
                <p style="text-align: center;"><strong>دولة قطر</strong></p>
                <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (البائع):</strong> {{seller_name}}</p>
                <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{seller_id}}</p>
                <p><strong>الطرف الثاني (المشتري):</strong> {{buyer_name}}</p>
                <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{buyer_id}}</p>
                <p><strong>نوع السيارة:</strong> {{car_type}}</p>
                <p><strong>موديل السيارة:</strong> {{car_model}}</p>
                <p><strong>سنة الصنع:</strong> {{car_year}}</p>
                <p><strong>رقم اللوحة:</strong> {{plate_number}}</p>
                <p><strong>رقم الشاصي:</strong> {{chassis_number}}</p>
                <p><strong>سعر البيع:</strong> {{price}} ريال قطري</p>
                <p>وقد تم الاتفاق على جميع الشروط والأحكام المذكورة أعلاه.</p>
                ''',
                'fields': {
                    'seller_name': 'اسم البائع',
                    'seller_id': 'رقم هوية البائع',
                    'buyer_name': 'اسم المشتري',
                    'buyer_id': 'رقم هوية المشتري',
                    'car_type': 'نوع السيارة',
                    'car_model': 'موديل السيارة',
                    'car_year': 'سنة الصنع',
                    'plate_number': 'رقم اللوحة',
                    'chassis_number': 'رقم الشاصي',
                    'price': 'السعر',
                    'hijri_date': 'التاريخ الهجري'
                }
            },
            {
                'name': 'إيجار سكني',
                'description': 'عقد إيجار عقار سكني في دولة قطر',
                'template': '''
                <h2 style="text-align: center;">عقد إيجار سكني</h2>
                <p style="text-align: center;"><strong>دولة قطر</strong></p>
                <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (المؤجر):</strong> {{landlord_name}}</p>
                <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{landlord_id}}</p>
                <p><strong>الطرف الثاني (المستأجر):</strong> {{tenant_name}}</p>
                <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{tenant_id}}</p>
                <p><strong>العقار المؤجر:</strong> {{property_description}}</p>
                <p><strong>المنطقة:</strong> {{area}}</p>
                <p><strong>رقم القطعة:</strong> {{plot_number}}</p>
                <p><strong>مدة الإيجار:</strong> {{duration}}</p>
                <p><strong>قيمة الإيجار الشهري:</strong> {{monthly_rent}} ريال قطري</p>
                <p><strong>قيمة الإيجار السنوي:</strong> {{annual_rent}} ريال قطري</p>
                <p><strong>مبلغ التأمين:</strong> {{security_deposit}} ريال قطري</p>
                ''',
                'fields': {
                    'landlord_name': 'اسم المؤجر',
                    'landlord_id': 'رقم هوية المؤجر',
                    'tenant_name': 'اسم المستأجر',
                    'tenant_id': 'رقم هوية المستأجر',
                    'property_description': 'وصف العقار',
                    'area': 'المنطقة',
                    'plot_number': 'رقم القطعة',
                    'duration': 'مدة الإيجار',
                    'monthly_rent': 'الإيجار الشهري',
                    'annual_rent': 'الإيجار السنوي',
                    'security_deposit': 'مبلغ التأمين',
                    'hijri_date': 'التاريخ الهجري'
                }
            },
            {
                'name': 'عقد عمل',
                'description': 'عقد عمل في دولة قطر',
                'template': '''
                <h2 style="text-align: center;">عقد عمل</h2>
                <p style="text-align: center;"><strong>دولة قطر</strong></p>
                <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (صاحب العمل):</strong> {{employer_name}}</p>
                <p><strong>السجل التجاري:</strong> {{commercial_register}}</p>
                <p><strong>الطرف الثاني (الموظف):</strong> {{employee_name}}</p>
                <p><strong>رقم الهوية/الإقامة:</strong> {{employee_id}}</p>
                <p><strong>المسمى الوظيفي:</strong> {{job_title}}</p>
                <p><strong>الراتب الأساسي:</strong> {{basic_salary}} ريال قطري</p>
                <p><strong>البدلات:</strong> {{allowances}} ريال قطري</p>
                <p><strong>إجمالي الراتب:</strong> {{total_salary}} ريال قطري</p>
                <p><strong>مدة العقد:</strong> {{contract_duration}}</p>
                <p><strong>ساعات العمل:</strong> {{working_hours}}</p>
                ''',
                'fields': {
                    'employer_name': 'اسم صاحب العمل',
                    'commercial_register': 'السجل التجاري',
                    'employee_name': 'اسم الموظف',
                    'employee_id': 'رقم هوية الموظف',
                    'job_title': 'المسمى الوظيفي',
                    'basic_salary': 'الراتب الأساسي',
                    'allowances': 'البدلات',
                    'total_salary': 'إجمالي الراتب',
                    'contract_duration': 'مدة العقد',
                    'working_hours': 'ساعات العمل',
                    'hijri_date': 'التاريخ الهجري'
                }
            },
            {
                'name': 'مقاولات',
                'description': 'عقد مقاولات في دولة قطر',
                'template': '''
                <h2 style="text-align: center;">عقد مقاولات</h2>
                <p style="text-align: center;"><strong>دولة قطر</strong></p>
                <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (صاحب العمل):</strong> {{client_name}}</p>
                <p><strong>الطرف الثاني (المقاول):</strong> {{contractor_name}}</p>
                <p><strong>رخصة المقاول:</strong> {{contractor_license}}</p>
                <p><strong>وصف المشروع:</strong> {{project_description}}</p>
                <p><strong>موقع المشروع:</strong> {{project_location}}</p>
                <p><strong>قيمة العقد:</strong> {{contract_value}} ريال قطري</p>
                <p><strong>مدة التنفيذ:</strong> {{execution_period}}</p>
                <p><strong>تاريخ البدء:</strong> {{start_date}}</p>
                <p><strong>تاريخ الانتهاء:</strong> {{end_date}}</p>
                ''',
                'fields': {
                    'client_name': 'اسم صاحب العمل',
                    'contractor_name': 'اسم المقاول',
                    'contractor_license': 'رخصة المقاول',
                    'project_description': 'وصف المشروع',
                    'project_location': 'موقع المشروع',
                    'contract_value': 'قيمة العقد',
                    'execution_period': 'مدة التنفيذ',
                    'start_date': 'تاريخ البدء',
                    'end_date': 'تاريخ الانتهاء',
                    'hijri_date': 'التاريخ الهجري'
                }
            }
        ]
    
    def __repr__(self):
        return f'<ContractType {self.name}>'
