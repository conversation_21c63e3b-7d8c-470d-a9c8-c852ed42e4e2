from datetime import datetime
from . import db

class ContractType(db.Model):
    __tablename__ = 'contract_types'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    template = db.Column(db.Text)  # Default template for this contract type
    fields = db.Column(db.JSON)  # Custom fields for this contract type
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contracts = db.relationship('Contract', backref='contract_type_ref', lazy=True)
    
    def __init__(self, name, description=None, template=None, fields=None):
        self.name = name
        self.description = description
        self.template = template
        self.fields = fields or {}
    
    def to_dict(self):
        """Convert contract type to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'template': self.template,
            'fields': self.fields,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def get_default_types():
        """Get default contract types"""
        return [
            {
                'name': 'بيع سيارة',
                'description': 'عقد بيع سيارة',
                'template': '''
                <h2 style="text-align: center;">عقد بيع سيارة</h2>
                <p>أنه في يوم {{date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (البائع):</strong> {{seller_name}}</p>
                <p><strong>الطرف الثاني (المشتري):</strong> {{buyer_name}}</p>
                <p><strong>نوع السيارة:</strong> {{car_type}}</p>
                <p><strong>رقم اللوحة:</strong> {{plate_number}}</p>
                <p><strong>سعر البيع:</strong> {{price}} ريال</p>
                ''',
                'fields': {
                    'seller_name': 'اسم البائع',
                    'buyer_name': 'اسم المشتري',
                    'car_type': 'نوع السيارة',
                    'plate_number': 'رقم اللوحة',
                    'price': 'السعر'
                }
            },
            {
                'name': 'إيجار',
                'description': 'عقد إيجار عقار',
                'template': '''
                <h2 style="text-align: center;">عقد إيجار</h2>
                <p>أنه في يوم {{date}} تم الاتفاق بين:</p>
                <p><strong>الطرف الأول (المؤجر):</strong> {{landlord_name}}</p>
                <p><strong>الطرف الثاني (المستأجر):</strong> {{tenant_name}}</p>
                <p><strong>العقار:</strong> {{property_description}}</p>
                <p><strong>مدة الإيجار:</strong> {{duration}}</p>
                <p><strong>قيمة الإيجار:</strong> {{rent_amount}} ريال</p>
                ''',
                'fields': {
                    'landlord_name': 'اسم المؤجر',
                    'tenant_name': 'اسم المستأجر',
                    'property_description': 'وصف العقار',
                    'duration': 'مدة الإيجار',
                    'rent_amount': 'قيمة الإيجار'
                }
            }
        ]
    
    def __repr__(self):
        return f'<ContractType {self.name}>'
