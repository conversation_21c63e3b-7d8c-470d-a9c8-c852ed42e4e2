#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to add all Qatar contract types to the database
"""

from app import create_app
from models import db
from models.contract_type import ContractType

def add_qatar_contract_types():
    """Add comprehensive contract types for Qatar"""
    
    contract_types = [
        {
            'name': 'بيع سيارة',
            'description': 'عقد بيع سيارة في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد بيع سيارة</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (البائع):</strong> {{seller_name}}</p>
            <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{seller_id}}</p>
            <p><strong>الطرف الثاني (المشتري):</strong> {{buyer_name}}</p>
            <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{buyer_id}}</p>
            <p><strong>نوع السيارة:</strong> {{car_type}}</p>
            <p><strong>موديل السيارة:</strong> {{car_model}}</p>
            <p><strong>سنة الصنع:</strong> {{car_year}}</p>
            <p><strong>رقم اللوحة:</strong> {{plate_number}}</p>
            <p><strong>رقم الشاصي:</strong> {{chassis_number}}</p>
            <p><strong>سعر البيع:</strong> {{price}} ريال قطري</p>
            <p>وقد تم الاتفاق على جميع الشروط والأحكام المذكورة أعلاه.</p>
            ''',
            'fields': {
                'seller_name': 'اسم البائع',
                'seller_id': 'رقم هوية البائع',
                'buyer_name': 'اسم المشتري',
                'buyer_id': 'رقم هوية المشتري',
                'car_type': 'نوع السيارة',
                'car_model': 'موديل السيارة',
                'car_year': 'سنة الصنع',
                'plate_number': 'رقم اللوحة',
                'chassis_number': 'رقم الشاصي',
                'price': 'السعر',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'شراء عقار',
            'description': 'عقد شراء عقار في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد شراء عقار</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (البائع):</strong> {{seller_name}}</p>
            <p><strong>الطرف الثاني (المشتري):</strong> {{buyer_name}}</p>
            <p><strong>وصف العقار:</strong> {{property_description}}</p>
            <p><strong>المنطقة:</strong> {{area}}</p>
            <p><strong>رقم القطعة:</strong> {{plot_number}}</p>
            <p><strong>المساحة:</strong> {{area_size}} متر مربع</p>
            <p><strong>سعر البيع:</strong> {{sale_price}} ريال قطري</p>
            ''',
            'fields': {
                'seller_name': 'اسم البائع',
                'buyer_name': 'اسم المشتري',
                'property_description': 'وصف العقار',
                'area': 'المنطقة',
                'plot_number': 'رقم القطعة',
                'area_size': 'المساحة',
                'sale_price': 'سعر البيع',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'إيجار سكني',
            'description': 'عقد إيجار عقار سكني في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد إيجار سكني</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (المؤجر):</strong> {{landlord_name}}</p>
            <p><strong>الطرف الثاني (المستأجر):</strong> {{tenant_name}}</p>
            <p><strong>العقار المؤجر:</strong> {{property_description}}</p>
            <p><strong>المنطقة:</strong> {{area}}</p>
            <p><strong>مدة الإيجار:</strong> {{duration}}</p>
            <p><strong>قيمة الإيجار الشهري:</strong> {{monthly_rent}} ريال قطري</p>
            <p><strong>قيمة الإيجار السنوي:</strong> {{annual_rent}} ريال قطري</p>
            <p><strong>مبلغ التأمين:</strong> {{security_deposit}} ريال قطري</p>
            ''',
            'fields': {
                'landlord_name': 'اسم المؤجر',
                'tenant_name': 'اسم المستأجر',
                'property_description': 'وصف العقار',
                'area': 'المنطقة',
                'duration': 'مدة الإيجار',
                'monthly_rent': 'الإيجار الشهري',
                'annual_rent': 'الإيجار السنوي',
                'security_deposit': 'مبلغ التأمين',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'إيجار تجاري',
            'description': 'عقد إيجار عقار تجاري في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد إيجار تجاري</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (المؤجر):</strong> {{landlord_name}}</p>
            <p><strong>الطرف الثاني (المستأجر):</strong> {{tenant_name}}</p>
            <p><strong>السجل التجاري للمستأجر:</strong> {{commercial_register}}</p>
            <p><strong>العقار المؤجر:</strong> {{property_description}}</p>
            <p><strong>نوع النشاط التجاري:</strong> {{business_type}}</p>
            <p><strong>قيمة الإيجار السنوي:</strong> {{annual_rent}} ريال قطري</p>
            ''',
            'fields': {
                'landlord_name': 'اسم المؤجر',
                'tenant_name': 'اسم المستأجر',
                'commercial_register': 'السجل التجاري',
                'property_description': 'وصف العقار',
                'business_type': 'نوع النشاط التجاري',
                'annual_rent': 'الإيجار السنوي',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'تقسيط',
            'description': 'عقد تقسيط في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد تقسيط</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (البائع):</strong> {{seller_name}}</p>
            <p><strong>الطرف الثاني (المشتري):</strong> {{buyer_name}}</p>
            <p><strong>السلعة/الخدمة:</strong> {{item_description}}</p>
            <p><strong>إجمالي المبلغ:</strong> {{total_amount}} ريال قطري</p>
            <p><strong>المقدم:</strong> {{down_payment}} ريال قطري</p>
            <p><strong>عدد الأقساط:</strong> {{installments_count}}</p>
            <p><strong>قيمة القسط الشهري:</strong> {{monthly_installment}} ريال قطري</p>
            ''',
            'fields': {
                'seller_name': 'اسم البائع',
                'buyer_name': 'اسم المشتري',
                'item_description': 'وصف السلعة/الخدمة',
                'total_amount': 'إجمالي المبلغ',
                'down_payment': 'المقدم',
                'installments_count': 'عدد الأقساط',
                'monthly_installment': 'القسط الشهري',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'عقد عمل',
            'description': 'عقد عمل في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد عمل</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (صاحب العمل):</strong> {{employer_name}}</p>
            <p><strong>السجل التجاري:</strong> {{commercial_register}}</p>
            <p><strong>الطرف الثاني (الموظف):</strong> {{employee_name}}</p>
            <p><strong>رقم الهوية/الإقامة:</strong> {{employee_id}}</p>
            <p><strong>المسمى الوظيفي:</strong> {{job_title}}</p>
            <p><strong>الراتب الأساسي:</strong> {{basic_salary}} ريال قطري</p>
            <p><strong>البدلات:</strong> {{allowances}} ريال قطري</p>
            <p><strong>إجمالي الراتب:</strong> {{total_salary}} ريال قطري</p>
            <p><strong>مدة العقد:</strong> {{contract_duration}}</p>
            ''',
            'fields': {
                'employer_name': 'اسم صاحب العمل',
                'commercial_register': 'السجل التجاري',
                'employee_name': 'اسم الموظف',
                'employee_id': 'رقم هوية الموظف',
                'job_title': 'المسمى الوظيفي',
                'basic_salary': 'الراتب الأساسي',
                'allowances': 'البدلات',
                'total_salary': 'إجمالي الراتب',
                'contract_duration': 'مدة العقد',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'خدمات استشارية',
            'description': 'عقد خدمات استشارية في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد خدمات استشارية</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (العميل):</strong> {{client_name}}</p>
            <p><strong>الطرف الثاني (المستشار):</strong> {{consultant_name}}</p>
            <p><strong>نوع الاستشارة:</strong> {{consultation_type}}</p>
            <p><strong>نطاق العمل:</strong> {{scope_of_work}}</p>
            <p><strong>أتعاب الاستشارة:</strong> {{consultation_fee}} ريال قطري</p>
            <p><strong>مدة تقديم الخدمة:</strong> {{service_duration}}</p>
            ''',
            'fields': {
                'client_name': 'اسم العميل',
                'consultant_name': 'اسم المستشار',
                'consultation_type': 'نوع الاستشارة',
                'scope_of_work': 'نطاق العمل',
                'consultation_fee': 'أتعاب الاستشارة',
                'service_duration': 'مدة تقديم الخدمة',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'مقاولات',
            'description': 'عقد مقاولات في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">عقد مقاولات</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (صاحب العمل):</strong> {{client_name}}</p>
            <p><strong>الطرف الثاني (المقاول):</strong> {{contractor_name}}</p>
            <p><strong>رخصة المقاول:</strong> {{contractor_license}}</p>
            <p><strong>وصف المشروع:</strong> {{project_description}}</p>
            <p><strong>موقع المشروع:</strong> {{project_location}}</p>
            <p><strong>قيمة العقد:</strong> {{contract_value}} ريال قطري</p>
            <p><strong>مدة التنفيذ:</strong> {{execution_period}}</p>
            ''',
            'fields': {
                'client_name': 'اسم صاحب العمل',
                'contractor_name': 'اسم المقاول',
                'contractor_license': 'رخصة المقاول',
                'project_description': 'وصف المشروع',
                'project_location': 'موقع المشروع',
                'contract_value': 'قيمة العقد',
                'execution_period': 'مدة التنفيذ',
                'hijri_date': 'التاريخ الهجري'
            }
        },
        {
            'name': 'وكالة قانونية',
            'description': 'عقد وكالة قانونية في دولة قطر',
            'template': '''
            <h2 style="text-align: center;">وكالة قانونية</h2>
            <p style="text-align: center;"><strong>دولة قطر</strong></p>
            <p>أنه في يوم {{date}} الموافق {{hijri_date}} تم الاتفاق بين:</p>
            <p><strong>الطرف الأول (الموكل):</strong> {{principal_name}}</p>
            <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{principal_id}}</p>
            <p><strong>الطرف الثاني (الوكيل):</strong> {{agent_name}}</p>
            <p><strong>رقم الهوية القطرية/الإقامة:</strong> {{agent_id}}</p>
            <p><strong>نطاق الوكالة:</strong> {{agency_scope}}</p>
            <p><strong>مدة الوكالة:</strong> {{agency_duration}}</p>
            <p><strong>أتعاب الوكالة:</strong> {{agency_fee}} ريال قطري</p>
            ''',
            'fields': {
                'principal_name': 'اسم الموكل',
                'principal_id': 'رقم هوية الموكل',
                'agent_name': 'اسم الوكيل',
                'agent_id': 'رقم هوية الوكيل',
                'agency_scope': 'نطاق الوكالة',
                'agency_duration': 'مدة الوكالة',
                'agency_fee': 'أتعاب الوكالة',
                'hijri_date': 'التاريخ الهجري'
            }
        }
    ]
    
    # Add contract types to database
    for contract_type_data in contract_types:
        # Check if contract type already exists
        existing = ContractType.query.filter_by(name=contract_type_data['name']).first()
        if not existing:
            contract_type = ContractType(
                name=contract_type_data['name'],
                description=contract_type_data['description'],
                template=contract_type_data['template'],
                fields=contract_type_data['fields']
            )
            db.session.add(contract_type)
            print(f"Added contract type: {contract_type_data['name']}")
        else:
            print(f"Contract type already exists: {contract_type_data['name']}")
    
    try:
        db.session.commit()
        print("Successfully added all Qatar contract types!")
    except Exception as e:
        db.session.rollback()
        print(f"Error adding contract types: {e}")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        add_qatar_contract_types()
